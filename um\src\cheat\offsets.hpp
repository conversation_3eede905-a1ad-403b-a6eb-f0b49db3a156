#pragma once
#include <Windows.h>

namespace Offset {

  // buttons
  inline DWORD ForceJump;
  inline DWORD ForceCrouch;
  inline DWORD ForceForward;
  inline DWORD ForceLeft;
  inline DWORD ForceRight;

  // offset
  inline DWORD dwPlantedC4;
  inline DWORD dwEntityList;
  inline DWORD dwGameEntitySystem;  // cs2_dumper::offsets::client_dll::dwGameEntitySystem
  inline DWORD dwGameEntitySystem_highestEntityIndex;  // cs2_dumper::offsets::client_dll::dwGameEntitySystem_highestEntityIndex
  inline DWORD dwViewMatrix;
  inline DWORD dwViewAngles;
  inline DWORD dwLocalPlayerController;  // cs2_dumper::offsets::client_dll::dwLocalPlayerController
  inline DWORD dwLocalPlayerPawn;
  inline DWORD dwGlobalVars;

  // client
  inline DWORD m_bDormant;
  inline DWORD m_vecAbsOrigin;

  namespace ActionTracking {
    inline DWORD m_perRoundStats;
    inline DWORD m_matchStats;
    inline DWORD m_iNumRoundKills;
    inline DWORD m_iNumRoundKillsHeadshots;
    inline DWORD m_unTotalRoundDamageDealt;
  }  // namespace ActionTracking

  namespace Entity {
    // DWORD Health = Offset::Pawn.CurrentHealth;
    // DWORD TeamID = Offset::Pawn.iTeamNum;
    inline DWORD m_bPawnIsAlive;
    inline DWORD m_hPlayerPawn;
    inline DWORD m_iszPlayerName;
    // outdated DWORD EnemySensor = 0x1440;
    inline DWORD m_flGravityScale;
  }  // namespace Entity

  namespace Pawn {
    // Services
    inline DWORD m_pMovementServices;   // CPlayer_MovementServices*
    inline DWORD m_pWeaponServices;     // CPlayer_WeaponServices*
    inline DWORD m_pBulletServices;     // CCSPlayer_BulletServices*
    inline DWORD m_pCameraServices;     // CPlayer_CameraServices*
    inline DWORD ViewModelServices;  // CPlayer_ViewModelServices*
    inline DWORD m_pClippingWeapon;    // C_CSWeaponBase*

    // View Related
    inline DWORD ViewModel;  // CCSPlayer_ViewModelServices::m_hViewModel
    inline DWORD m_nCrouchState;
    inline DWORD m_bIsScoped;
    inline DWORD m_angEyeAngles;
    inline DWORD m_vecViewOffset;
    inline DWORD m_vecLastClipCameraPos;
    inline DWORD m_iDesiredFOV;
    inline DWORD m_iFOVStart;

    // Status
    inline DWORD m_bIsDefusing;
    inline DWORD m_totalHitsOnServer;
    inline DWORD m_vOldOrigin;            // C_BasePlayerPawn::m_vOldOrigin
    inline DWORD m_ArmorValue;   // C_CSPlayerPawn::m_ArmorValue
    inline DWORD m_iMaxHealth;      // C_BaseEntity::m_iMaxHealth
    inline DWORD m_iHealth;  // C_BaseEntity::m_iHealth

    // Scene & Bones
    inline DWORD m_pGameSceneNode;  // C_BaseEntity::m_pGameSceneNode
    inline DWORD BoneArray;      // cs2_dumper::schemas::client_dll::CGameSceneNode::m_vecOrigin

    // Combat Related
    inline DWORD m_iShotsFired;
    inline DWORD m_flFlashMaxAlpha;
    inline DWORD m_flFlashDuration;
    inline DWORD m_aimPunchAngle;  // C_CSPlayerPawn::m_aimPunchAngle
    inline DWORD m_aimPunchCache;

    // Identification & Team
    inline DWORD m_iIDEntIndex;
    inline DWORD m_iTeamNum;

    // Flags & States
    inline DWORD m_fFlags;
    inline DWORD m_entitySpottedState;
    inline DWORD m_bSpottedByMask;  // C_CSPlayerPawn::entitySpottedState + EntitySpottedState_t::bSpottedByMask
    inline DWORD SPOTTED;
    inline DWORD m_vecAbsVelocity;
    inline DWORD m_bIsBuyMenuOpen;
  }  // namespace Pawn

  namespace GlobalVar {
    inline DWORD RealTime         = 0x00;
    inline DWORD FrameCount       = 0x04;
    inline DWORD MaxClients       = 0x10;
    inline DWORD IntervalPerTick  = 0x14;
    inline DWORD CurrentTime      = 0x2C;
    inline DWORD CurrentTime2     = 0x30;
    inline DWORD TickCount        = 0x40;
    inline DWORD IntervalPerTick2 = 0x44;
    inline DWORD CurrentNetchan   = 0x0048;
    inline DWORD CurrentMap       = 0x0180;
    inline DWORD CurrentMapName   = 0x0188;
  }  // namespace GlobalVar

  namespace PlayerController {
    inline DWORD m_pActionTrackingServices;
    inline DWORD m_hPawn;
    inline DWORD m_pObserverServices;
    inline DWORD m_hObserverTarget;
    inline DWORD m_hController;
    inline DWORD m_iPawnArmor;
    inline DWORD m_bPawnHasDefuser;
    inline DWORD m_bPawnHasHelmet;
  }  // namespace PlayerController

  namespace EconEntity {
    inline DWORD m_AttributeManager;  // C_AttributeContainer
    inline DWORD m_nFallbackPaintKit;
    inline DWORD m_nFallbackSeed;
    inline DWORD m_flFallbackWear;
    inline DWORD m_nFallbackStatTrak;
    inline DWORD m_szCustomName;

    inline DWORD m_iEntityQuality;  // EconItemView::m_iEntityQuality
    inline DWORD m_iItemIDHigh;     // EconItemView::m_iItemIDHigh
  }  // namespace EconEntity

  namespace WeaponBaseData {
    inline DWORD WeaponDataPTR = 0x368;
    inline DWORD m_szName;
    inline DWORD m_iClip1;    // C_BasePlayerWeapon::m_iClip1
    inline DWORD m_iMaxClip1;  // CBasePlayerWeaponVData::m_iMaxClip1
    inline DWORD m_flCycleTime;
    inline DWORD m_flPenetration;
    inline DWORD m_WeaponType;
    inline DWORD m_flInaccuracyMove;  // CCSWeaponBaseVData::m_flInaccuracyMove
    inline DWORD m_bInReload;

    inline DWORD WeaponSize = 0x50;
    inline DWORD m_hActiveWeapon;
    inline DWORD m_Item;  // C_AttributeContainer::m_Item
    inline DWORD m_iItemDefinitionIndex;
    inline DWORD m_MeshGroupMask;  // CModelState::m_MeshGroupMask
  }  // namespace WeaponBaseData

  namespace C4 {
    inline DWORD m_bBeingDefused;  // bool
    inline DWORD m_flDefuseCountDown;
    inline DWORD m_nBombSite;
  }  // namespace C4

  namespace InGameMoneyServices {
    inline DWORD m_pInGameMoneyServices;
    inline DWORD m_iAccount;
    inline DWORD m_iTotalCashSpent;
    inline DWORD m_iCashSpentThisRound;
  }  // namespace InGameMoneyServices

  namespace SmokeGrenadeProjectile  // C_BaseCSGrenadeProjectile
  {
    inline DWORD     m_nSmokeEffectTickBegin;     // int32_t
    inline DWORD     m_bDidSmokeEffect;           // bool
    inline DWORD     m_nRandomSeed;               // int32_t
    inline DWORD     m_vSmokeColor;               // Vector
    inline DWORD     m_vSmokeDetonationPos;       // Vector
    inline DWORD     m_VoxelFrameData;            // CUtlVector<uint8_t>
    inline DWORD     m_bSmokeVolumeDataReceived;  // bool
    inline uintptr_t m_bSmokeEffectSpawned;       // bool
  }  // namespace SmokeGrenadeProjectile

}  // namespace Offset
