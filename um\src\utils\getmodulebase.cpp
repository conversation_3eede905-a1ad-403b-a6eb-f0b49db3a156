#include "pch.h"

#include <Windows.h>
#include <TlHelp32.h>
#include <cstdint>
#include "getmodulebase.hpp" // Include the header file we just created

namespace utils
{
  uintptr_t get_module_base(const DWORD pid, const wchar_t* module_name) {
    uintptr_t module_base = 0;

    // Snap-shot of process' modules (dlls).
    HANDLE snap_shot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, pid);
    if (snap_shot == INVALID_HANDLE_VALUE)
      return module_base;

    MODULEENTRY32W entry = {};
    entry.dwSize = sizeof(decltype(entry));

    if (Module32FirstW(snap_shot, &entry) == TRUE) {
      if (wcsstr(module_name, entry.szModule) != nullptr)
        module_base = reinterpret_cast<uintptr_t>(entry.modBaseAddr);
      else {
        while (Module32NextW(snap_shot, &entry) == TRUE) {
          if (wcsstr(module_name, entry.szModule) != nullptr) {
            module_base = reinterpret_cast<uintptr_t>(entry.modBaseAddr);
            break;
          }
        }
      }
    }

    CloseHandle(snap_shot);

    return module_base;
  }
}
