#include "pch.h"
#include "health_animations.hpp"

// Static member definition
std::unordered_map<int, float> HealthAnimations::previousHealthValues;

// ===========================
// MAIN API
// ===========================

float HealthAnimations::GetAnimatedHealth(int entityId, float targetHealth) {
    // Check if we have an active animation for this entity
    if (AnimationManager::HasActiveHealthAnimation(entityId)) {
        return AnimationManager::GetHealthBarValue(entityId);
    }

    // No animation active, return target value
    return targetHealth;
}

void HealthAnimations::ResetPlayerHealth(int entityId) {
    AnimationManager::RemovePlayerAnimations(entityId);
    previousHealthValues.erase(entityId);
}

void HealthAnimations::ResetAllHealthAnimations() {
    AnimationManager::ClearBarAnimations();
    previousHealthValues.clear();
}

// ===========================
// AUTOMATIC REGISTRATION
// ===========================

void HealthAnimations::UpdateHealthAnimation(int entityId, float targetHealth) {
    // Get the current animated health value (or previous value if no animation)
    float currentHealth = GetAnimatedHealth(entityId, targetHealth);

    // Check if we have a previous value stored
    auto it = previousHealthValues.find(entityId);
    if (it != previousHealthValues.end()) {
        currentHealth = it->second;
    }

    // Only register animation if values are different
    if (std::abs(currentHealth - targetHealth) > 0.1f) {
        RegisterHealthAnimation(entityId, currentHealth, targetHealth);
    }

    // Store the new target value as the previous value for next frame
    previousHealthValues[entityId] = targetHealth;
}

// ===========================
// INTERNAL HELPERS
// ===========================

void HealthAnimations::RegisterHealthAnimation(int entityId, float currentHealth, float targetHealth) {
    // Use the centralized animation manager
    AnimationManager::RegisterHealthBar(entityId, currentHealth, targetHealth);
}
