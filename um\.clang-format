---
AllowShortFunctionsOnASingleLine: None
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false
AlignArrayOfStructures: Left
BasedOnStyle: Google
BraceWrapping: 
  AfterCaseLabel: false
  AfterClass: false
  AfterControlStatement: false
  AfterEnum: true
  AfterFunction: false
  AfterNamespace: false
  AfterObjCDeclaration: false
  AfterStruct: true
  AfterUnion: false
  AfterExternBlock: false
  BeforeCatch: false
  BeforeElse: false
  IndentBraces: false
  SplitEmptyFunction: false
  SplitEmptyRecord: false
  SplitEmptyNamespace: false
  BeforeLambdaBody: false
  BeforeWhile: false
BreakBeforeBraces: Custom
ColumnLimit: 0
DerivePointerAlignment: false
FixNamespaceComments: true
IndentExternBlock: Indent
IndentWidth: 2
Language: Cpp
NamespaceIndentation: All
PointerAlignment: Left
SortIncludes: false
SpacesInParentheses: true
SpacesInSquareBrackets: false
Standard: c++20
AlignConsecutiveAssignments: true
AlignConsecutiveDeclarations: true
PenaltyBreakBeforeFirstCallParameter: 1000
PenaltyBreakComment: 1000
PenaltyBreakFirstLessLess: 1000
PenaltyBreakString: 1000
PenaltyExcessCharacter: 1000000
PenaltyReturnTypeOnItsOwnLine: 1000
PenaltyIndentedWhitespace: 0
PenaltyBreakAssignment: 1000
...
