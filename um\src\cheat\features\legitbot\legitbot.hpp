#pragma once
#include "../../gamedata.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../../render/render.hpp"
#include "../../../math/vector.hpp"
#include "../../entity.hpp"

#include <thread>
#include <chrono>
#include <Windows.h>
#include <optional>

// ===========================
// LEGITBOT CONSTANTS
// ===========================
namespace LegitbotConstants {
    // Default hotkey for aimbot (X key)
    constexpr int DEFAULT_AIMBOT_HOTKEY = 0x58;

    // Bone memory offset multiplier
    constexpr int BONE_OFFSET_MULTIPLIER = 32;

    // Smoothing constants
    constexpr float MIN_SMOOTHNESS = 1.0f;
    constexpr float DISTANCE_SCALING_FACTOR = 1000.0f;

    // Deadzone constants
    constexpr float BASE_DEADZONE = 0.2f;
    constexpr float MAX_DEADZONE = 2.0f;

    // Triggerbot timing constants
    constexpr int CLICK_DURATION_MS = 10;
    constexpr int RECOVERY_DELAY_MS = 50;

    // Entity validation constants
    constexpr int MIN_CROSSHAIR_TARGET_ID = 1;
    constexpr int MAX_CROSSHAIR_TARGET_ID = 1024; // Erweitert für CS2 Entity-System
}

class Legitbot
{
public:
	void doAimbot(const Reader& reader);
	void Triggerbot();

private:
	std::optional<Vector> findClosest(const std::vector<Vector>& playerPositions);
	void MoveMouseToPlayer(Vector position);
	void PerformTriggerbotShot();

	float accumulatedX = 0.0f;
	float accumulatedY = 0.0f;
};

inline Legitbot legitbot;