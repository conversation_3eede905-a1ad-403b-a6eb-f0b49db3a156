#include "pch.h"
#include "legitbot.hpp"
#include "../../entity.hpp"
#include "../../globals.hpp"
#include "../../gamedata.hpp"
#include <iostream>

// ===========================
// AIMBOT IMPLEMENTATION
// ===========================

void Legitbot::doAimbot(const Reader& reader)
{
	// Early exit if aimbot is not enabled or hotkey is not pressed
	if (!globals::Legitbot::enabled || !GetAsyncKeyState(globals::Legitbot::hotkey)) {
		return;
	}

	view_matrix_t viewMatrix = GameData::getViewMatrix();
	auto playerList = reader.getPlayerListCopy();

	std::vector<Vector> playerPositions;
	playerPositions.reserve(playerList.size()); // Reserve space for better performance

	int localTeam = GameData::getLocalTeam();

	for (const auto& player : playerList)
	{
		// Team check - skip teammates if enabled
		if (player.team == localTeam && globals::Legitbot::teamcheck)
			continue;

		// Visibility check - skip non-spotted enemies if enabled
		if (!player.PlayerSpotted && player.team != localTeam && globals::Legitbot::visiblecheck)
			continue;

		// Get the 3D position using the new abstracted method
		Vector playerPosition = player.getBonePosition(bones::head);

		// Skip if we couldn't get a valid position
		if (playerPosition.IsZero())
			continue;

		Vector screenPosition;
		if (Vector::world_to_screen(viewMatrix, playerPosition, screenPosition))
		{
			playerPositions.push_back(screenPosition);
		}
	}

	// Find and move to closest target
	auto closest_player = findClosest(playerPositions);
	if (closest_player.has_value())
	{
		MoveMouseToPlayer(closest_player.value());
	}
}

std::optional<Vector> Legitbot::findClosest(const std::vector<Vector>& playerPositions)
{
	if (playerPositions.empty()) return std::nullopt;

	// Get current mouse position instead of screen center
	POINT currentMousePos;
	GetCursorPos(&currentMousePos);
	Vector current_mouse_pos{
		static_cast<float>(currentMousePos.x),
		static_cast<float>(currentMousePos.y),
		0.0f
	};

	float max_distance_sq = globals::Legitbot::radius * globals::Legitbot::radius;
	float closest_distance_sq = FLT_MAX;
	std::optional<Vector> closest = std::nullopt;

	for (const auto& pos : playerPositions) {
		float dx = pos.x - current_mouse_pos.x;
		float dy = pos.y - current_mouse_pos.y;
		float distance_sq = dx*dx + dy*dy;

		if (distance_sq < closest_distance_sq && distance_sq < max_distance_sq) {
			closest_distance_sq = distance_sq;
			closest = pos;
		}
	}
	return closest;
}

void Legitbot::MoveMouseToPlayer(Vector position)
{
	if (position.IsZero())
		return;

	POINT currentMousePos;
	GetCursorPos(&currentMousePos);
	Vector currentPos{
		static_cast<float>(currentMousePos.x),
		static_cast<float>(currentMousePos.y),
		0.0f
	};

	float deltaX = position.x - currentPos.x;
	float deltaY = position.y - currentPos.y;

	// Use constants for better maintainability
	const float base_smoothness = max(globals::Legitbot::smoothness, LegitbotConstants::MIN_SMOOTHNESS);
	const float distance = std::sqrt(deltaX * deltaX + deltaY * deltaY);
	const float adaptive_smoothness = base_smoothness * (1.0f + distance / LegitbotConstants::DISTANCE_SCALING_FACTOR);

	float stepX = deltaX / adaptive_smoothness;
	float stepY = deltaY / adaptive_smoothness;

	accumulatedX += stepX;
	accumulatedY += stepY;

	LONG moveX = static_cast<LONG>(std::round(accumulatedX));
	LONG moveY = static_cast<LONG>(std::round(accumulatedY));

	accumulatedX -= moveX;
	accumulatedY -= moveY;

	// Use constants for deadzone calculation
	const float deadzone = min(LegitbotConstants::BASE_DEADZONE * adaptive_smoothness, LegitbotConstants::MAX_DEADZONE);
	if (std::abs(deltaX) < deadzone && std::abs(deltaY) < deadzone) {
		accumulatedX += deltaX;
		accumulatedY += deltaY;
		return;
	}

	mouse_event(MOUSEEVENTF_MOVE, moveX, moveY, 0, 0);
}

void Legitbot::Triggerbot()
{
	// Check if triggerbot is enabled and menu is not visible
	if (!globals::Triggerbot::enabled || globals::overlayVisible) {
		return;
	}

	// Check if the hotkey is pressed
	bool hotkeyPressed = GetAsyncKeyState(globals::Triggerbot::hotkey) & 0x8000;

	// Static variable to track key state changes
	static bool wasPressed = false;

	// Print ID on every key press (rising edge: was not pressed, now pressed)
	if (hotkeyPressed && !wasPressed) {
		// Check if GameData system is ready
		if (GameData::isInitialized()) {
			uintptr_t localPlayerController = GameData::getLocalPlayerController();
			if (localPlayerController) {
				// Get the entity ID of the crosshair target and print it
				int crosshairTargetId = GameData::getCrosshairTargetId(localPlayerController);
				// std::cout << "ID: " << crosshairTargetId << std::endl;
			}
		}
	}

	// Update the previous state (this happens regardless of other conditions)
	wasPressed = hotkeyPressed;

	// Exit if hotkey is not pressed
	if (!hotkeyPressed) {
		return;
	}

	// Check if GameData system is ready
	if (!GameData::isInitialized()) {
		return;
	}

	uintptr_t localPlayerController = GameData::getLocalPlayerController();
	if (!localPlayerController) {
		return;
	}

	// Get local player pawn
	uintptr_t localPlayerPawn = GameData::getLocalPlayerPawn();
	if (!localPlayerPawn) {
		return;
	}

	// Get game variables
	auto* gameVars = GameVars::getInstance();
	if (!gameVars || !gameVars->initialized()) {
		return;
	}

	HANDLE driverHandle = gameVars->getDriver();
	uintptr_t clientBase = gameVars->getClient();

	// Get entity list
	const auto entityList = driver::read_memory<std::uintptr_t>(driverHandle, clientBase + Offset::dwEntityList);
	if (!entityList) {
		return;
	}

	// Get local team
	int localTeam = driver::read_memory<int>(driverHandle, localPlayerPawn + Offset::Pawn::m_iTeamNum);

	// Get crosshair target ID
	int crosshairId = driver::read_memory<int>(driverHandle, localPlayerPawn + Offset::Pawn::m_iIDEntIndex);

	if (crosshairId > 0) {
		// Get list entry
		std::uintptr_t listEntry = driver::read_memory<std::uintptr_t>(driverHandle, entityList + 0x8 * (crosshairId >> 9) + 0x10);
		if (!listEntry) {
			return;
		}

		// Get entity pawn
		const auto entityPawn = driver::read_memory<std::uintptr_t>(driverHandle, listEntry + 120 * (crosshairId & 0x1FF));
		if (!entityPawn) {
			return;
		}

		// Check health
		int health = driver::read_memory<int>(driverHandle, entityPawn + Offset::Pawn::m_iHealth);
		if (health <= 0) {
			return;
		}

		// Check team (if teamcheck is enabled)
		int entityTeam = driver::read_memory<int>(driverHandle, entityPawn + Offset::Pawn::m_iTeamNum);
		bool isEnemy = !globals::Triggerbot::teamcheck || (localTeam != entityTeam);

		if (isEnemy && health > 0) {
			PerformTriggerbotShot();
		}
	}
}

void Legitbot::PerformTriggerbotShot() {
	static auto lastShotTime = std::chrono::steady_clock::now();
	auto currentTime = std::chrono::steady_clock::now();

	// Prevent spam clicking - minimum delay between shots
	if (std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastShotTime).count() < LegitbotConstants::RECOVERY_DELAY_MS) {
		return;
	}

	// Perform mouse click
	mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
	std::this_thread::sleep_for(std::chrono::milliseconds(LegitbotConstants::CLICK_DURATION_MS));
	mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);

	lastShotTime = currentTime;
}