#pragma once
#include "../core/animation_manager.hpp"

class MenuAnimations {
public:
    // === FADE ANIMATIONS ===
    static void RegisterFadeIn(const std::string& elementId, float duration = 0.3f);
    static void RegisterFadeOut(const std::string& elementId, float duration = 0.3f);
    static float GetFadeAlpha(const std::string& elementId);

    // === SLIDE ANIMATIONS ===
    static void RegisterSlideIn(const std::string& elementId, float startPos, float endPos, float duration = 0.4f);
    static void RegisterSlideOut(const std::string& elementId, float startPos, float endPos, float duration = 0.4f);
    static float GetSlidePosition(const std::string& elementId);

    // === TRANSITION ANIMATIONS ===
    static void RegisterTransition(const std::string& elementId, float startValue, float endValue, float duration = 0.5f);
    static float GetTransitionValue(const std::string& elementId);

    // === HOVER COLOR ANIMATIONS ===
    static void RegisterHoverColorIn(const std::string& elementId, const ImVec4& normalColor, const ImVec4& hoverColor, float duration = 0.2f);
    static void RegisterHoverColorOut(const std::string& elementId, const ImVec4& hoverColor, const ImVec4& normalColor, float duration = 0.15f);
    static ImVec4 GetHoverColor(const std::string& elementId, const ImVec4& defaultColor);

    // === HOVER STATE MANAGEMENT ===
    static ImVec4 HandleButtonHover(const std::string& elementId, bool isHovered, const ImVec4& normalColor, const ImVec4& hoverColor);

    // === MENU ELEMENT FADE ANIMATIONS ===
    static void RegisterElementFadeIn(const std::string& elementId, float delay = 0.0f, float duration = 0.4f);
    static void RegisterElementFadeOut(const std::string& elementId, float duration = 0.3f);
    static float GetElementFadeAlpha(const std::string& elementId);

    // === BOX CONTENT FADE ANIMATIONS ===
    static void RegisterBoxContentFade(const std::string& boxId, float delay = 0.0f, float duration = 0.5f);
    static float GetBoxContentAlpha(const std::string& boxId);

    // === DELAYED ANIMATION SYSTEM ===
    static void RegisterDelayedFadeIn(const std::string& elementId, float delay, float duration = 0.4f);
    static void StartBoxAnimationSequence(const std::string& boxId);

    // === MENU OPEN/CLOSE ANIMATIONS ===
    static void StartMenuOpenAnimation(float duration = 0.5f);
    static void StartMenuCloseAnimation(float duration = 0.3f);
    static float GetMenuAlpha(bool menuShouldBeVisible = true);
    static bool IsMenuAnimating();

    // === ANIMATION STATE BLOCKING ===
    static bool IsMenuOpening();
    static bool IsMenuClosing();
    static bool CanToggleMenu();

    // === COLORPICKER POPUP ANIMATIONS ===
    static void RegisterColorPickerPopupOpen(const std::string& popupId, float duration = 0.25f);
    static void RegisterColorPickerPopupClose(const std::string& popupId, float duration = 0.2f);
    static float GetColorPickerPopupAlpha(const std::string& popupId);
    static void RegisterColorSelectionTransition(const std::string& elementId, const ImVec4& fromColor, const ImVec4& toColor, float duration = 0.3f);
    static ImVec4 GetColorSelectionValue(const std::string& elementId, const ImVec4& defaultColor);

    // === CHECKBOX ANIMATIONS ===
    static void RegisterCheckboxHoverIn(const std::string& checkboxId, float duration = 0.15f);
    static void RegisterCheckboxHoverOut(const std::string& checkboxId, float duration = 0.2f);
    static void RegisterCheckboxStateChange(const std::string& checkboxId, bool newState, float duration = 0.25f);
    static float GetCheckboxHoverAlpha(const std::string& checkboxId);
    static float GetCheckboxCheckmarkAlpha(const std::string& checkboxId);

    // === CHECKBOX STATE TRACKING ===
    static void SetCheckboxHoverState(const std::string& checkboxId, bool isHovering);
    static bool GetCheckboxHoverState(const std::string& checkboxId);

    // === SLIDER SMOOTH ANIMATIONS ===
    static void RegisterSliderValueChange(const std::string& sliderId, float currentValue, float targetValue, float duration = 0.1f);
    static float GetSliderAnimatedValue(const std::string& sliderId, float currentValue);
    static bool IsSliderAnimating(const std::string& sliderId);

    // === SEPARATOR ANIMATIONS ===
    static void RegisterSeparatorFadeIn(const std::string& separatorId, float duration = 0.5f);
    static float GetSeparatorFadeProgress(const std::string& separatorId);

    // === UTILITY FUNCTIONS ===
    static bool IsAnimationActive(const std::string& elementId);
    static bool HasAnimationEntry(const std::string& elementId);
    static void ClearAllMenuAnimations();

private:
    // === INTERNAL HELPERS ===
    static void RegisterUIAnimation(const std::string& elementId, float startValue, float endValue,
                                   float duration, AnimationType type);

    // === ANIMATION STATE TRACKING ===
    enum class MenuAnimationState {
        IDLE,       // No animation running
        OPENING,    // Menu is fading in
        CLOSING     // Menu is fading out
    };
    static MenuAnimationState currentAnimationState;
};
