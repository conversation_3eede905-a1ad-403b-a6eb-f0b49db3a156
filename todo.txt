1. UI-Anpassungen

    Klick- und Active-Farbe für alle Buttons überarbeiten

    „X“-Button zum Schließen des Menüs ergänzen

2. Duck-<PERSON><PERSON>

    Head- und Skeleton-Dots beim Ducken nicht mehr skalieren

    Stattdessen Head-Pos-Bone (oder einen andere<PERSON> Bone) duplizieren und die Z-Koordinate um +3/+4/+5 versetzen, so bleibt die Größe konstant

3. Sound & Hitsounds

    Fix: Beim Programmstart die Hitsound-Lautstärke korrekt laden

    Test-Button implementieren, der den aktuell ausgewählten Hitsound sofort abspielt

    Dropdown mit Default-Hitsounds hinzufügen

4. Triggerbot

    Delay zwischen einzelnen Schüssen implementieren

    Separaten allgemeinen Triggerbot-Delay hinzufügen

5. ESP & Overlays

    ESP-Preview für Offscreen (Pfeil + Punkt, wie LuckyCharms)

    Bomben-ESP: Timer, Seite, Icon und Name anzeigen

    Fallengelassene Waffen: Text + Icon darstellen

    No-Scope-Crosshair und Hitmarker-Preview einbauen

    Velocity-Anzeige (Geschwindigkeits-Text) ergänzen

    Proximity-Warning für Projektile mit Icon

    Smoke-Timer-Icon (21 s)

    Molly-ESP mit Timer

    Origin-Follow-Line zeichnen

    Hitmarker mit Fade-Out-Animation versehen
