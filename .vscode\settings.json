{"files.exclude": {"**/build": false, "**/*.suo": true, "**/*.user": true, "**/nebula-cc.sln": true, "**/*.vcxproj": true, "**/*.vcxproj.filters": true, "**/*.vcxproj.user": true, "**/.vs": true, "**/.vscode": false, "**/Linking": true, "**/imgui": true, "**/x64": true, "**/km": false, "**/rewrite": true, "**/*.natvis": true, "**/*.inf": true, "**/*.json": false, "**/*.md": true, "**/*.gitignore": true, "**/*.git": true, "**/imgui.ini": true, "**/.clang-format": true, "**/todo.txt": true, "**/todo2.0.txt": true}, "explorer.excludeGitIgnore": false, "explorer.compactFolders": true, "files.associations": {"*.h": "cpp", "*.hpp": "cpp", "*.cpp": "cpp", "algorithm": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "compare": "cpp", "concepts": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "exception": "cpp", "filesystem": "cpp", "format": "cpp", "forward_list": "cpp", "fstream": "cpp", "functional": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "memory": "cpp", "mutex": "cpp", "new": "cpp", "numeric": "cpp", "optional": "cpp", "ostream": "cpp", "ranges": "cpp", "ratio": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "utility": "cpp", "valarray": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp"}, "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Auxiliary/Build/vcvarsall.bat", "C_Cpp.errorSquiggles": "disabled"}