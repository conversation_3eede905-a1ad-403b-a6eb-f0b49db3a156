#include "pch.h"

#include "vector.hpp"
#include <iostream>
#include <string>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

int screenWidth = GetSystemMetrics(SM_CXSCREEN); // 0
int screenHeight = GetSystemMetrics(SM_CYSCREEN); // 1

const Vector& Vector::operator-(const Vector& other) const {
    static Vector result;
    result.x = x - other.x;
    result.y = y - other.y;
    result.z = z - other.z;
    return result;
}

const Vector& Vector::operator+(const Vector& other) const {
    static Vector result;
    result.x = x + other.x;
    result.y = y + other.y;
    result.z = z + other.z;
    return result;
}

const Vector& Vector::operator/(const float factor) const {
    static Vector result;
    result.x = x / factor;
    result.y = y / factor;
    result.z = z / factor;
    return result;
}

const Vector& Vector::operator*(const float factor) const {
    static Vector result;
    result.x = x * factor;
    result.y = y * factor;
    result.z = z * factor;
    return result;
}

bool Vector::isInvalid() const {
  // �berpr�fe, ob einer der Werte in wissenschaftlicher Notation ist
  if (std::abs(x) < 1e-5f || std::abs(y) < 1e-5f || std::abs(z) < 1e-5f ||
    std::to_string(x).find('e') != std::string::npos ||
    std::to_string(y).find('e') != std::string::npos ||
    std::to_string(z).find('e') != std::string::npos) {
    return true;  // Filtert Werte in der N�he von 0 oder ung�ltige Werte
  }
  return false;
}

// w2s, alot of explanations already exist
const bool Vector::world_to_screen(view_matrix_t vm, Vector& in, Vector& out)
{
  out.x = vm[0][0] * in.x + vm[0][1] * in.y + vm[0][2] * in.z + vm[0][3];
  out.y = vm[1][0] * in.x + vm[1][1] * in.y + vm[1][2] * in.z + vm[1][3];
  out.z = vm[2][0] * in.x + vm[2][1] * in.y + vm[2][2] * in.z + vm[2][3];

  float width = vm[3][0] * in.x + vm[3][1] * in.y + vm[3][2] * in.z + vm[3][3];

  if (width < 0.01f) {
    return false;
  }

  float inverseWidth = 1.f / width;

  out.x *= inverseWidth;
  out.y *= inverseWidth;
  out.z *= inverseWidth;

  float x = screenWidth / 2;
  float y = screenHeight / 2;

  x += 0.5f * out.x * screenWidth + 0.5f;
  y -= 0.5f * out.y * screenHeight + 0.5f;

  out.x = x;
  out.y = y;

  return true;
}

const bool Vector::IsZero() {
  return x == 0.0f && y == 0.0f && z == 0.0f;
}

float Vector::length() const {
    return sqrtf(x * x + y * y + z * z);
}

#define DEG2RAD(deg) ((deg) * M_PI / 180.f)

void Vector::Angle(const Vector& angles, Vector& forward) {
  float sp, sy, cp, cy;
  sy = sin(DEG2RAD(angles.y));
  cy = cos(DEG2RAD(angles.y));
  sp = sin(DEG2RAD(angles.x));
  cp = cos(DEG2RAD(angles.x));
  forward.x = cp * cy;
  forward.y = cp * sy;
  forward.z = -sp;
}

void Vector::Normalize(Vector& angles) {
  if (angles.x > 89.0f) angles.x = 89.0f;
  if (angles.x < -89.0f) angles.x = -89.0f;
  while (angles.y > 180.0f) angles.y -= 360.0f;
  while (angles.y < -180.0f) angles.y += 360.0f;
  angles.z = 0.0f;
}

void Vector::Add(Vector& vec, Vector& vec2) {
  vec.x += vec2.x;
  vec.y += vec2.y;
  vec.z += vec2.z;
}
