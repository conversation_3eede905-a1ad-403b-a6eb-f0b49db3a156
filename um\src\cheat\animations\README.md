# Neues Animation System - <PERSON>bersicht

Das Animation-System wurde komplett neu strukturiert und ist jetzt viel übersichtlicher und benutzerfreundlicher!

## 📁 Neue Struktur

```
cheat/animations/
├── core/                          # Basis Animation System
│   ├── animation_manager.hpp/cpp  # Zentraler Manager für alle Animationen
│   ├── animation_types.hpp        # Alle Animation-Datentypen
│   └── animation_utils.hpp        # Hilfsfunktionen & Easing
├── esp/                          # ESP-spezifische Animationen
│   ├── death_animations.hpp/cpp  # Death Fade System
│   ├── health_animations.hpp/cpp # Health Bar Animationen
│   └── armor_animations.hpp/cpp  # Armor Bar Animationen
└── ui/                           # UI/Menu Animationen
    └── menu_animations.hpp/cpp   # Menu Fade/Slide Animationen
```

## 🚀 Automatische Registrierung - Nur 3 Parameter!

### Death Animations
```cpp
// Automatisch: Position, Daten, Duration
DeathAnimations::RegisterPlayerDeath(entityId, position, eyePosition, boneArray, 
                                   playerName, flags, weaponIndex, health, armor, team, spotted);
```

### Health/Armor Bar Animationen
```cpp
// Automatisch: EntityId, Current, Target
HealthAnimations::UpdateHealthAnimation(entityId, currentHealth, targetHealth);
ArmorAnimations::UpdateArmorAnimation(entityId, currentArmor, targetArmor);
```

### UI Animationen
```cpp
// Automatisch: ElementId, Start, End, Duration
MenuAnimations::RegisterFadeIn("menu_main", 0.3f);
MenuAnimations::RegisterSlideIn("sidebar", -200.0f, 0.0f, 0.4f);
```

## 🎯 Einfache Abfrage

```cpp
// Death Fade Alpha abrufen
float alpha = AnimationManager::GetDeathFadeAlpha(entityId);

// Animierte Health/Armor Werte abrufen
float health = HealthAnimations::GetAnimatedHealth(entityId, targetHealth);
float armor = ArmorAnimations::GetAnimatedArmor(entityId, targetArmor);

// UI Animation Werte abrufen
float fadeAlpha = MenuAnimations::GetFadeAlpha("menu_main");
float slidePos = MenuAnimations::GetSlidePosition("sidebar");
```

## ✨ Automatische Features

- **Automatisches Cleanup**: Abgelaufene Animationen werden automatisch entfernt
- **Intelligente Registrierung**: Animationen werden nur erstellt wenn sich Werte ändern
- **Einheitliche API**: Alle Animation-Typen verwenden das gleiche System
- **Performance-optimiert**: Effiziente Speicherung und Abfrage
- **Easing Functions**: Verschiedene Interpolations-Modi verfügbar

## 🔧 Migration vom alten System

### Vorher (unübersichtlich):
```cpp
// Verschiedene Manager überall verteilt
DeathAnimationManager::RegisterPlayerDeath(id, data);
HealthBarAnimator::UpdateAnimation(id, value);
ArmorBarAnimator::UpdateAnimation(id, value);
```

### Nachher (übersichtlich):
```cpp
// Alles zentral und automatisch
DeathAnimations::RegisterPlayerDeath(id, pos, ...);
HealthAnimations::UpdateHealthAnimation(id, current, target);
ArmorAnimations::UpdateArmorAnimation(id, current, target);
```

## 🎨 Easing Functions verfügbar

- `Linear` - Gleichmäßige Interpolation
- `EaseInQuad` - Langsam starten, schnell werden
- `EaseOutQuad` - Schnell starten, langsam werden  
- `EaseInOutQuad` - Langsam-schnell-langsam
- `SmoothStep` - Glatte S-Kurve
- `SmootherStep` - Noch glattere S-Kurve

Das neue System ist viel sauberer, lesbarer und einfacher zu verwenden! 🎉
