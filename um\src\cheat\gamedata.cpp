#include "pch.h"
#include "gamedata.hpp"

// Static member definitions
bool GameData::cachingEnabled = true;
std::mutex GameData::cacheMutex;
GameData::CachedData GameData::cache;

// ===========================
// CORE GAME DATA ACCESS
// ===========================

view_matrix_t GameData::getViewMatrix() {
    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return view_matrix_t{};
    }

    return driver::read_memory<view_matrix_t>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwViewMatrix
    );
}

uintptr_t GameData::getLocalPlayerController() {
    if (cachingEnabled && isCacheValid()) {
        std::lock_guard<std::mutex> lock(cacheMutex);
        return cache.localPlayerController;
    }

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    uintptr_t result = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwLocalPlayerController
    );

    if (cachingEnabled) {
        updateCache();
    }

    return result;
}

uintptr_t GameData::getLocalPlayerPawn() {
    if (cachingEnabled && isCacheValid()) {
        std::lock_guard<std::mutex> lock(cacheMutex);
        return cache.localPlayerPawn;
    }

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    uintptr_t result = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwLocalPlayerPawn
    );

    if (cachingEnabled) {
        updateCache();
    }

    return result;
}

uintptr_t GameData::getEntityList() {
    if (cachingEnabled && isCacheValid()) {
        std::lock_guard<std::mutex> lock(cacheMutex);
        return cache.entityList;
    }

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    uintptr_t result = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwEntityList
    );

    if (cachingEnabled) {
        updateCache();
    }

    return result;
}

int GameData::getLocalTeam() {
    if (cachingEnabled && isCacheValid()) {
        std::lock_guard<std::mutex> lock(cacheMutex);
        return cache.localTeam;
    }

    uintptr_t localPlayer = getLocalPlayerController();
    if (!localPlayer) {
        return 0;
    }

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    int result = driver::read_memory<int>(
        gameVars->getDriver(),
        localPlayer + Offset::Pawn::m_iTeamNum
    );

    if (cachingEnabled) {
        updateCache();
    }

    return result;
}

Vector GameData::getViewAngles() {
    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return Vector{};
    }

    return driver::read_memory<Vector>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwViewAngles
    );
}

// ===========================
// PLAYER DATA ACCESS
// ===========================

Vector GameData::getPlayerPosition(uintptr_t playerPawn) {
    if (!playerPawn) return Vector{};

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return Vector{};
    }

    return driver::read_memory<Vector>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_vOldOrigin
    );
}

int GameData::getPlayerHealth(uintptr_t playerPawn) {
    if (!playerPawn) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<int>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_iHealth
    );
}

int GameData::getPlayerArmor(uintptr_t playerPawn) {
    if (!playerPawn) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<int>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_ArmorValue
    );
}

int GameData::getPlayerTeam(uintptr_t playerPawn) {
    if (!playerPawn) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<int>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_iTeamNum
    );
}

uint32_t GameData::getPlayerFlags(uintptr_t playerPawn) {
    if (!playerPawn) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<uint32_t>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_fFlags
    );
}

bool GameData::getPlayerSpotted(uintptr_t playerPawn) {
    if (!playerPawn) return false;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return false;
    }

    return driver::read_memory<bool>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_entitySpottedState + Offset::Pawn::m_bSpottedByMask
    );
}

int GameData::getCrosshairTargetId(uintptr_t localPlayerController) {
    if (!localPlayerController) return -1;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return -1;
    }

    // Get local player pawn first
    uintptr_t localPlayerPawn = getLocalPlayerPawn();
    if (!localPlayerPawn) return -1;

    // Read the crosshair entity index from the local player pawn
    return driver::read_memory<int>(
        gameVars->getDriver(),
        localPlayerPawn + Offset::Pawn::m_iIDEntIndex
    );
}

uintptr_t GameData::getPlayerPawn(int entityId) {
    if (entityId <= 0 || entityId > 2048) { // Erweitert für CS2 Entity-System
        return 0;
    }

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    uintptr_t entityList = getEntityList();
    if (!entityList) return 0;

    // Calculate entity address using the same method as in entity.cpp
    uintptr_t listEntry = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        entityList + (8 * (entityId & 0x7FFF) >> 9) + 16
    );
    if (!listEntry) return 0;

    uintptr_t entityController = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        listEntry + 120 * (entityId & 0x1FF)
    );
    if (!entityController) return 0;

    // Get the pawn handle from the controller
    uint32_t pawnHandle = driver::read_memory<uint32_t>(
        gameVars->getDriver(),
        entityController + Offset::PlayerController::m_hPawn
    );
    if (!pawnHandle) return 0;

    // Calculate pawn address
    uintptr_t pawnListEntry = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        entityList + (8 * (pawnHandle & 0x7FFF) >> 9) + 16
    );
    if (!pawnListEntry) return 0;

    uintptr_t entityPawn = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        pawnListEntry + 120 * (pawnHandle & 0x1FF)
    );

    return entityPawn;
}

// ===========================
// WEAPON DATA ACCESS
// ===========================

uintptr_t GameData::getActiveWeapon(uintptr_t playerPawn) {
    if (!playerPawn) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_pClippingWeapon
    );
}

uint16_t GameData::getWeaponItemIndex(uintptr_t weaponPtr) {
    if (!weaponPtr) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<uint16_t>(
        gameVars->getDriver(),
        weaponPtr + Offset::EconEntity::m_AttributeManager +
        Offset::WeaponBaseData::m_Item + Offset::WeaponBaseData::m_iItemDefinitionIndex
    );
}

int GameData::getShotsFired(uintptr_t playerPawn) {
    if (!playerPawn) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<int>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_iShotsFired
    );
}

bool GameData::getIsScoped(uintptr_t playerPawn) {
    if (!playerPawn) return false;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return false;
    }

    return driver::read_memory<bool>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_bIsScoped
    );
}

// ===========================
// BONE DATA ACCESS
// ===========================

uint64_t GameData::getBoneArray(uintptr_t playerPawn) {
    if (!playerPawn) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    uint64_t gameSceneNode = driver::read_memory<uint64_t>(
        gameVars->getDriver(),
        playerPawn + Offset::Pawn::m_pGameSceneNode
    );

    if (!gameSceneNode) return 0;

    return driver::read_memory<uint64_t>(
        gameVars->getDriver(),
        gameSceneNode + Offset::Pawn::BoneArray + 0x80
    );
}

Vector GameData::getBonePosition(uint64_t boneArray, int boneIndex) {
    if (!boneArray) return Vector{};

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return Vector{};
    }

    return driver::read_memory<Vector>(
        gameVars->getDriver(),
        boneArray + static_cast<uintptr_t>(boneIndex) * 32
    );
}

// ===========================
// ENTITY DATA ACCESS
// ===========================

Vector GameData::getEntityPosition(uintptr_t entityPtr) {
    if (!entityPtr) return Vector{};

    uintptr_t gameSceneNode = getEntityGameSceneNode(entityPtr);
    if (!gameSceneNode) return Vector{};

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return Vector{};
    }

    return driver::read_memory<Vector>(
        gameVars->getDriver(),
        gameSceneNode + Offset::m_vecAbsOrigin
    );
}

uintptr_t GameData::getEntityGameSceneNode(uintptr_t entityPtr) {
    if (!entityPtr) return 0;

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        return 0;
    }

    return driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        entityPtr + Offset::Pawn::m_pGameSceneNode
    );
}

// ===========================
// UTILITY FUNCTIONS
// ===========================

bool GameData::isInitialized() {
    auto* gameVars = getGameVars();
    return gameVars && gameVars->initialized();
}

HANDLE GameData::getDriver() {
    auto* gameVars = getGameVars();
    if (!gameVars) return nullptr;
    return gameVars->getDriver();
}

uintptr_t GameData::getClientBase() {
    auto* gameVars = getGameVars();
    if (!gameVars) return 0;
    return gameVars->getClient();
}

// ===========================
// CACHING SYSTEM
// ===========================

void GameData::setCachingEnabled(bool enabled) {
    std::lock_guard<std::mutex> lock(cacheMutex);
    cachingEnabled = enabled;
    if (!enabled) {
        cache.isValid = false;
    }
}

void GameData::clearCache() {
    std::lock_guard<std::mutex> lock(cacheMutex);
    cache.isValid = false;
}

// ===========================
// PRIVATE HELPER FUNCTIONS
// ===========================

bool GameData::isCacheValid() {
    std::lock_guard<std::mutex> lock(cacheMutex);
    if (!cache.isValid) return false;

    auto now = std::chrono::steady_clock::now();
    return (now - cache.lastUpdate) < CACHE_TIMEOUT;
}

void GameData::updateCache() {
    std::lock_guard<std::mutex> lock(cacheMutex);

    auto* gameVars = getGameVars();
    if (!gameVars || !gameVars->initialized()) {
        cache.isValid = false;
        return;
    }

    // Update cached values
    cache.localPlayerController = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwLocalPlayerController
    );

    cache.localPlayerPawn = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwLocalPlayerPawn
    );

    cache.entityList = driver::read_memory<uintptr_t>(
        gameVars->getDriver(),
        gameVars->getClient() + Offset::dwEntityList
    );

    if (cache.localPlayerController) {
        cache.localTeam = driver::read_memory<int>(
            gameVars->getDriver(),
            cache.localPlayerController + Offset::Pawn::m_iTeamNum
        );
    }

    cache.lastUpdate = std::chrono::steady_clock::now();
    cache.isValid = true;
}

GameVars* GameData::getGameVars() {
    return GameVars::getInstance();
}
