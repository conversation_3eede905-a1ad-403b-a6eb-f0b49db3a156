#include "pch.h"
#include "platform.hpp"
#include <Windows.h>

namespace platform {
    bool is_key_pressed(int key_code) {
        return (GetAsyncKeyState(key_code) & 1);
    }

    bool is_key_down(int key_code) {
        return (GetAsyncKeyState(key_code) & 0x8000);
    }

    Point get_cursor_position() {
        POINT p;
        GetCursorPos(&p);
        return {p.x, p.y};
    }

    void move_mouse(int dx, int dy) {
        mouse_event(MOUSEEVENTF_MOVE, dx, dy, 0, 0);
    }

    void mouse_left_down() {
        mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
    }

    void mouse_left_up() {
        mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
    }
}
