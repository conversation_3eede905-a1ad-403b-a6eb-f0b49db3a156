#pragma once

#include <map>
#include <string>

enum class ItemIndex {
    Invalid = 0,
    WeaponDeagle = 1,
    WeaponElite = 2,
    WeaponFiveseven = 3,
    WeaponGlock = 4,
    WeaponAk47 = 7,
    WeaponAug = 8,
    WeaponAwp = 9,
    WeaponFamas = 10,
    WeaponG3sg1 = 11,
    WeaponGalilar = 13,
    WeaponM249 = 14,
    WeaponM4a1 = 16,
    WeaponMac10 = 17,
    WeaponP90 = 19,
    WeaponMp5sd = 23,
    WeaponUmp45 = 24,
    WeaponXm1014 = 25,
    WeaponBizon = 26,
    WeaponMag7 = 27,
    WeaponNegev = 28,
    WeaponSawedoff = 29,
    WeaponTec9 = 30,
    WeaponTaser = 31,
    WeaponHkp2000 = 32,
    WeaponMp7 = 33,
    WeaponMp9 = 34,
    WeaponNova = 35,
    WeaponP250 = 36,
    WeaponScar20 = 38,
    WeaponSg556 = 39,
    WeaponSsg08 = 40,
    WeaponKnifegg = 41,
    WeaponKnife = 42,
    WeaponFlashbang = 43,
    WeaponHegrenade = 44,
    WeaponSmokegrenade = 45,
    WeaponMolotov = 46,
    WeaponDecoy = 47,
    WeaponIncgrenade = 48,
    WeaponC4 = 49,
    ItemKevlar = 50,
    ItemAssaultsuit = 51,
    ItemHeavyassaultsuit = 52,
    ItemNvg = 54,
    ItemDefuser = 55,
    ItemCutters = 56,
    WeaponHealthshot = 57,
    MusickitDefault = 58,
    WeaponKnifeT = 59,
    WeaponM4a1Silencer = 60,
    WeaponUspSilencer = 61,
    WeaponCz75a = 63,
    WeaponRevolver = 64,
    WeaponTagrenade = 68,
    WeaponFists = 69,
    WeaponBreachcharge = 70,
    WeaponTablet = 72,
    WeaponMelee = 74,
    WeaponAxe = 75,
    WeaponHammer = 76,
    WeaponSpanner = 78,
    WeaponKnifeGhost = 80,
    WeaponFirebomb = 81,
    WeaponDiversion = 82,
    WeaponFragGrenade = 83,
    WeaponSnowball = 84,
    WeaponBayonet = 500,
    WeaponKnifeFlip = 505,
    WeaponKnifeGut = 506,
    WeaponKnifeKarambit = 507,
    WeaponKnifeM9Bayonet = 508,
    WeaponKnifeTactical = 509,
    WeaponKnifeFalchion = 512,
    WeaponKnifeSurvivalBowie = 514,
    WeaponKnifeButterfly = 515,
    WeaponKnifePush = 516,
    WeaponKnifeUrsus = 519,
    WeaponKnifeGypsyJackknife = 520,
    WeaponKnifeStiletto = 522,
    WeaponKnifeWidowmaker = 523,
    Musickit = 1314,
    StuddedBloodhoundGloves = 5027,
    TGloves = 5028,
    CtGloves = 5029,
    SportyGloves = 5030,
    SlickGloves = 5031,
    LeatherHandwraps = 5032,
    MotorcycleGloves = 5033, // Motorcycle gloves item
    SpecialistGloves = 5034,
    StuddedHydraGloves = 5035,
};

struct ItemData {
    const char* name;
    const char* icon;
};

inline std::map<ItemIndex, ItemData> itemDataMap = {
    {ItemIndex::WeaponDeagle, {"Desert Eagle", "A"}},
    {ItemIndex::WeaponElite, {"Dual Berettas", "B"}},
    {ItemIndex::WeaponFiveseven, {"Five-SeveN", "C"}},
    {ItemIndex::WeaponGlock, {"Glock-18", "D"}},
    {ItemIndex::WeaponAk47, {"AK-47", "W"}},
    {ItemIndex::WeaponAug, {"AUG", "U"}},
    {ItemIndex::WeaponAwp, {"AWP", "Z"}},
    {ItemIndex::WeaponFamas, {"FAMAS", "R"}},
    {ItemIndex::WeaponG3sg1, {"G3SG1", "X"}},
    {ItemIndex::WeaponGalilar, {"Galil AR", "Q"}},
    {ItemIndex::WeaponM249, {"M249", "g"}},
    {ItemIndex::WeaponM4a1, {"M4A4", "S"}},
    {ItemIndex::WeaponMac10, {"MAC-10", "K"}},
    {ItemIndex::WeaponP90, {"P90", "O"}},
    {ItemIndex::WeaponMp5sd, {"MP5-SD", ""}},
    {ItemIndex::WeaponUmp45, {"UMP-45", "L"}},
    {ItemIndex::WeaponXm1014, {"XM1014", "b"}},
    {ItemIndex::WeaponBizon, {"PP-Bizon", "M"}},
    {ItemIndex::WeaponMag7, {"MAG-7", "d"}},
    {ItemIndex::WeaponNegev, {"Negev", "f"}},
    {ItemIndex::WeaponSawedoff, {"Sawed-Off", "c"}},
    {ItemIndex::WeaponTec9, {"Tec-9", "H"}},
    {ItemIndex::WeaponTaser, {"Zeus x27", "h"}},
    {ItemIndex::WeaponHkp2000, {"P2000", "E"}},
    {ItemIndex::WeaponMp7, {"MP7", "N"}},
    {ItemIndex::WeaponMp9, {"MP9", "R"}},
    {ItemIndex::WeaponNova, {"Nova", "e"}},
    {ItemIndex::WeaponP250, {"P250", "F"}},
    {ItemIndex::WeaponScar20, {"SCAR-20", "Y"}},
    {ItemIndex::WeaponSg556, {"SG 553", "V"}},
    {ItemIndex::WeaponSsg08, {"SSG 08", "a"}},
    {ItemIndex::WeaponKnifegg, {"Golden Knife", "]"}},
    {ItemIndex::WeaponKnife, {"Knife", "["}},
    {ItemIndex::WeaponFlashbang, {"Flashbang", "i"}},
    {ItemIndex::WeaponHegrenade, {"HE Grenade", "j"}},
    {ItemIndex::WeaponSmokegrenade, {"Smoke Grenade", "k"}},
    {ItemIndex::WeaponMolotov, {"Molotov", "l"}},
    {ItemIndex::WeaponDecoy, {"Decoy Grenade", "m"}},
    {ItemIndex::WeaponIncgrenade, {"Incendiary Grenade", "n"}},
    {ItemIndex::WeaponC4, {"C4 Explosive", "o"}},
    {ItemIndex::ItemKevlar, {"Kevlar Vest", ""}},
    {ItemIndex::ItemAssaultsuit, {"Kevlar + Helmet", ""}},
    {ItemIndex::ItemHeavyassaultsuit, {"Heavy Assault Suit", ""}},
    {ItemIndex::ItemNvg, {"Night Vision Goggles", ""}},
    {ItemIndex::ItemDefuser, {"Defuse Kit", ""}},
    {ItemIndex::ItemCutters, {"Wire Cutters", ""}},
    {ItemIndex::WeaponHealthshot, {"Medi-Shot", ""}},
    {ItemIndex::MusickitDefault, {"Music Kit", ""}},
    {ItemIndex::WeaponKnifeT, {"Knife", "["}},
    {ItemIndex::WeaponM4a1Silencer, {"M4A1-S", "T"}},
    {ItemIndex::WeaponUspSilencer, {"USP-S", "G"}},
    {ItemIndex::WeaponCz75a, {"CZ75-Auto", "I"}},
    {ItemIndex::WeaponRevolver, {"R8 Revolver", "J"}},
    {ItemIndex::WeaponTagrenade, {"Tactical Awareness Grenade", ""}},
    {ItemIndex::WeaponFists, {"Bare Hands", ""}},
    {ItemIndex::WeaponBreachcharge, {"Breach Charge", ""}},
    {ItemIndex::WeaponTablet, {"Tablet", ""}},
    {ItemIndex::WeaponMelee, {"Melee Weapon", ""}},
    {ItemIndex::WeaponAxe, {"Axe", ""}},
    {ItemIndex::WeaponHammer, {"Hammer", ""}},
    {ItemIndex::WeaponSpanner, {"Wrench", ""}},
    {ItemIndex::WeaponKnifeGhost, {"Spectral Shiv", ""}},
    {ItemIndex::WeaponFirebomb, {"Firebomb", ""}},
    {ItemIndex::WeaponDiversion, {"Diversion Device", ""}},
    {ItemIndex::WeaponFragGrenade, {"Frag Grenade", ""}},
    {ItemIndex::WeaponSnowball, {"Snowball", ""}},
    {ItemIndex::WeaponBayonet, {"Bayonet", ""}},
    {ItemIndex::WeaponKnifeFlip, {"Flip Knife", ""}},
    {ItemIndex::WeaponKnifeGut, {"Gut Knife", ""}},
    {ItemIndex::WeaponKnifeKarambit, {"Karambit", ""}},
    {ItemIndex::WeaponKnifeM9Bayonet, {"M9 Bayonet", ""}},
    {ItemIndex::WeaponKnifeTactical, {"Tactical Knife", ""}},
    {ItemIndex::WeaponKnifeFalchion, {"Falchion Knife", ""}},
    {ItemIndex::WeaponKnifeSurvivalBowie, {"Bowie Knife", ""}},
    {ItemIndex::WeaponKnifeButterfly, {"Butterfly Knife", ""}},
    {ItemIndex::WeaponKnifePush, {"Shadow Daggers", ""}},
    {ItemIndex::WeaponKnifeUrsus, {"Ursus Knife", ""}},
    {ItemIndex::WeaponKnifeGypsyJackknife, {"Navaja Knife", ""}},
    {ItemIndex::WeaponKnifeStiletto, {"Stiletto Knife", ""}},
    {ItemIndex::WeaponKnifeWidowmaker, {"Talon Knife", ""}},
    {ItemIndex::Musickit, {"Music Kit", ""}},
    {ItemIndex::StuddedBloodhoundGloves, {"Bloodhound Gloves", ""}},
    {ItemIndex::TGloves, {"T Gloves", ""}},
    {ItemIndex::CtGloves, {"CT Gloves", ""}},
    {ItemIndex::SportyGloves, {"Sport Gloves", ""}},
    {ItemIndex::SlickGloves, {"Driver Gloves", ""}},
    {ItemIndex::LeatherHandwraps, {"Hand Wraps", ""}},
    {ItemIndex::MotorcycleGloves, {"Moto Gloves", ""}},
    {ItemIndex::SpecialistGloves, {"Specialist Gloves", ""}},
    {ItemIndex::StuddedHydraGloves, {"Hydra Gloves", ""}},
};

inline const char* GetItemName(ItemIndex itemIndex) {
    auto it = itemDataMap.find(itemIndex);
    if (it != itemDataMap.end()) {
        return it->second.name;
    }
    return "Unknown Weapon";
}

inline const char* GetItemIcon(ItemIndex itemIndex) {
    auto it = itemDataMap.find(itemIndex);
    if (it != itemDataMap.end()) {
        return it->second.icon;
    }
    return "";
}