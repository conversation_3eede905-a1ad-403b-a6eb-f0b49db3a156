#include "pch.h"

#include "OffsetsUpdater.hpp"
#include "offsets.hpp"
#include "../utils/network.hpp"


std::string downloadFile( const std::string& url ) {
  return network::download_content( url );
}

Offsets parseOffsetsFromString(const std::string& data) {
  Offsets offsets;
  std::istringstream stream(data);
  std::string line;
  std::string prevLine;  // Speichert die vorherige Zeile

  while (std::getline(stream, line)) {
    // Suche nach Zeilen wie: constexpr std::ptrdiff_t <name> = <value>; // <type>
    if (line.find("constexpr") != std::string::npos) {
      std::istringstream iss(line);
      std::string type, dummy, name, equalSign;
      uint32_t value;

      iss >> type >> dummy >> name >> equalSign >> std::hex >> value;

      // Entferne unerwünschte Zeichen vom Namen (z. B. ';')
      name = name.substr(0, name.find('='));
      name.erase(std::remove_if(name.begin(), name.end(), ::isspace), name.end());

      // Verarbeite spezifische Offsets basierend auf umgebenden Werten
      if (name == "m_AttributeManager") {
        // Überprüfen, ob die vorherige Zeile "m_bAttributesInitialized" ist
        if (prevLine.find("m_bAttributesInitialized") != std::string::npos) {
          offsets.data[name] = value;  // Speichern von m_AttributeManager
        }
      } else if (name == "m_entitySpottedState") {
        // Überprüfen, ob die vorherige Zeile "m_vHeadConstraintOffset" ist
        if (prevLine.find("m_vHeadConstraintOffset") != std::string::npos) {
          offsets.data[name] = value;  // Speichern von m_entitySpottedState
        }
      } else {
        // Andere Offsets wie gewohnt speichern
        offsets.data[name] = value;
      }
    }
    prevLine = line;  // Setze die aktuelle Zeile als vorherige für die nächste Iteration
  }

  return offsets;
}


void useclientOffsets( const Offsets& offsets ) {
  try {
    Offset::m_bDormant     = offsets.get( "m_bDormant" );
    Offset::m_vecAbsOrigin = offsets.get( "m_vecAbsOrigin" );

    Offset::ActionTracking::m_perRoundStats           = offsets.get( "m_perRoundStats" );
    Offset::ActionTracking::m_matchStats              = offsets.get( "m_matchStats" );
    Offset::ActionTracking::m_iNumRoundKills          = offsets.get( "m_iNumRoundKills" );
    Offset::ActionTracking::m_iNumRoundKillsHeadshots = offsets.get( "m_iNumRoundKillsHeadshots" );
    Offset::ActionTracking::m_unTotalRoundDamageDealt = offsets.get( "m_unTotalRoundDamageDealt" );

    Offset::Entity::m_bPawnIsAlive   = offsets.get( "m_bPawnIsAlive" );
    Offset::Entity::m_hPlayerPawn    = offsets.get( "m_hPlayerPawn" );
    Offset::Entity::m_iszPlayerName  = offsets.get( "m_iszPlayerName" );
    Offset::Entity::m_flGravityScale = offsets.get( "m_flGravityScale" );

    Offset::Pawn::m_pMovementServices = offsets.get( "m_pMovementServices" );
    Offset::Pawn::m_pWeaponServices   = offsets.get( "m_pWeaponServices" );
    Offset::Pawn::m_pBulletServices   = offsets.get( "m_pBulletServices" );
    Offset::Pawn::m_pCameraServices   = offsets.get( "m_pCameraServices" );
    Offset::Pawn::m_pClippingWeapon   = offsets.get( "m_pClippingWeapon" );

    Offset::Pawn::m_nCrouchState = offsets.get( "m_nCrouchState" );

    Offset::Pawn::m_bIsScoped         = offsets.get( "m_bIsScoped" );
    Offset::Pawn::m_bIsDefusing       = offsets.get( "m_bIsDefusing" );
    Offset::Pawn::m_totalHitsOnServer = offsets.get( "m_totalHitsOnServer" );
    Offset::Pawn::m_vOldOrigin        = offsets.get( "m_vOldOrigin" );
    // std::cout << "1.Offset::Pawn::Pos: 0x" << std::hex << Offset::Pawn::Pos << std::dec << std::endl;

    Offset::Pawn::m_ArmorValue           = offsets.get( "m_ArmorValue" );
    Offset::Pawn::m_iMaxHealth           = offsets.get( "m_iMaxHealth" );
    Offset::Pawn::m_iHealth              = offsets.get( "m_iHealth" );
    Offset::Pawn::m_pGameSceneNode       = offsets.get( "m_pGameSceneNode" );
    Offset::Pawn::BoneArray              = offsets.get( "m_modelState" );
    Offset::Pawn::m_angEyeAngles         = offsets.get( "m_angEyeAngles" );
    Offset::Pawn::m_vecViewOffset        = offsets.get( "m_vecViewOffset" );
    Offset::Pawn::m_vecLastClipCameraPos = offsets.get( "m_vecLastClipCameraPos" );
    Offset::Pawn::m_iShotsFired          = offsets.get( "m_iShotsFired" );
    Offset::Pawn::m_flFlashMaxAlpha      = offsets.get( "m_flFlashMaxAlpha" );
    Offset::Pawn::m_flFlashDuration      = offsets.get( "m_flFlashDuration" );
    Offset::Pawn::m_aimPunchAngle        = offsets.get( "m_aimPunchAngle" );
    Offset::Pawn::m_aimPunchCache        = offsets.get( "m_aimPunchCache" );
    Offset::Pawn::m_iIDEntIndex          = offsets.get( "m_iIDEntIndex" );
    Offset::Pawn::m_iTeamNum             = offsets.get( "m_iTeamNum" );
    Offset::Pawn::m_iDesiredFOV          = offsets.get( "m_iDesiredFOV" );
    Offset::Pawn::m_iFOVStart            = offsets.get( "m_iFOVStart" );
    Offset::Pawn::m_fFlags               = offsets.get( "m_fFlags" );
    Offset::Pawn::m_bSpottedByMask       = offsets.get( "m_bSpottedByMask" );
    Offset::Pawn::m_entitySpottedState   = offsets.get( "m_entitySpottedState" );

    // std::cout << "Offset for m_entitySpottedState: 0x" << std::hex << Offset::Pawn::entitySpottedState << std::dec << std::endl;
    // std::cout << "Offset for m_bSpottedByMask: 0x" << std::hex << Offset::Pawn::bSpottedByMask << std::dec << std::endl;

    Offset::Pawn::m_vecAbsVelocity = offsets.get( "m_vecAbsVelocity" );
    Offset::Pawn::m_bIsBuyMenuOpen = offsets.get( "m_bIsBuyMenuOpen" );

    Offset::PlayerController::m_pActionTrackingServices = offsets.get( "m_pActionTrackingServices" );
    Offset::PlayerController::m_hPawn                   = offsets.get( "m_hPawn" );
    Offset::PlayerController::m_pObserverServices       = offsets.get( "m_pObserverServices" );
    Offset::PlayerController::m_hObserverTarget         = offsets.get( "m_hObserverTarget" );
    Offset::PlayerController::m_hController             = offsets.get( "m_hController" );
    Offset::PlayerController::m_iPawnArmor              = offsets.get( "m_iPawnArmor" );
    Offset::PlayerController::m_bPawnHasDefuser         = offsets.get( "m_bPawnHasDefuser" );
    Offset::PlayerController::m_bPawnHasHelmet          = offsets.get( "m_bPawnHasHelmet" );

    // std::cout << "Offset for m_pActionTrackingServices: 0x" << std::hex << Offset::PlayerController::m_pActionTrackingServices << std::dec << std::endl;

    Offset::EconEntity::m_AttributeManager  = offsets.get( "m_AttributeManager" );
    Offset::EconEntity::m_nFallbackPaintKit = offsets.get( "m_nFallbackPaintKit" );
    Offset::EconEntity::m_nFallbackSeed     = offsets.get( "m_nFallbackSeed" );
    Offset::EconEntity::m_flFallbackWear    = offsets.get( "m_flFallbackWear" );
    Offset::EconEntity::m_nFallbackStatTrak = offsets.get( "m_nFallbackStatTrak" );
    Offset::EconEntity::m_szCustomName      = offsets.get( "m_szCustomName" );
    Offset::EconEntity::m_iEntityQuality    = offsets.get( "m_iEntityQuality" );
    Offset::EconEntity::m_iItemIDHigh       = offsets.get( "m_iItemIDHigh" );

    Offset::WeaponBaseData::m_szName               = offsets.get( "m_szName" );
    Offset::WeaponBaseData::m_iClip1               = offsets.get( "m_iClip1" );
    Offset::WeaponBaseData::m_iMaxClip1            = offsets.get( "m_iMaxClip1" );
    Offset::WeaponBaseData::m_flCycleTime          = offsets.get( "m_flCycleTime" );
    Offset::WeaponBaseData::m_flPenetration        = offsets.get( "m_flPenetration" );
    Offset::WeaponBaseData::m_WeaponType           = offsets.get( "m_WeaponType" );
    Offset::WeaponBaseData::m_flInaccuracyMove     = offsets.get( "m_flInaccuracyMove" );
    Offset::WeaponBaseData::m_bInReload            = offsets.get( "m_bInReload" );
    Offset::WeaponBaseData::m_hActiveWeapon        = offsets.get( "m_hActiveWeapon" );
    Offset::WeaponBaseData::m_Item                 = offsets.get( "m_Item" );
    Offset::WeaponBaseData::m_iItemDefinitionIndex = offsets.get( "m_iItemDefinitionIndex" ); // std::cout << "Offset for itemDefinitionIndex: 0x" << std::hex << Offset::WeaponBaseData::ItemDefinitionIndex << std::dec << std::endl;
    Offset::WeaponBaseData::m_MeshGroupMask        = offsets.get( "m_MeshGroupMask" );

    Offset::C4::m_bBeingDefused     = offsets.get( "m_bBeingDefused" );
    Offset::C4::m_flDefuseCountDown = offsets.get( "m_flDefuseCountDown" );
    Offset::C4::m_nBombSite         = offsets.get( "m_nBombSite" );

    Offset::InGameMoneyServices::m_pInGameMoneyServices = offsets.get( "m_pInGameMoneyServices" );
    Offset::InGameMoneyServices::m_iAccount             = offsets.get( "m_iAccount" );
    Offset::InGameMoneyServices::m_iTotalCashSpent      = offsets.get( "m_iTotalCashSpent" );
    Offset::InGameMoneyServices::m_iCashSpentThisRound  = offsets.get( "m_iCashSpentThisRound" );

    Offset::SmokeGrenadeProjectile::m_nSmokeEffectTickBegin    = offsets.get( "m_nSmokeEffectTickBegin" );
    Offset::SmokeGrenadeProjectile::m_bDidSmokeEffect          = offsets.get( "m_bDidSmokeEffect" );
    Offset::SmokeGrenadeProjectile::m_nRandomSeed              = offsets.get( "m_nRandomSeed" );
    Offset::SmokeGrenadeProjectile::m_vSmokeColor              = offsets.get( "m_vSmokeColor" );
    Offset::SmokeGrenadeProjectile::m_vSmokeDetonationPos      = offsets.get( "m_vSmokeDetonationPos" );
    Offset::SmokeGrenadeProjectile::m_VoxelFrameData           = offsets.get( "m_VoxelFrameData" );
    Offset::SmokeGrenadeProjectile::m_bSmokeVolumeDataReceived = offsets.get( "m_bSmokeVolumeDataReceived" );
    Offset::SmokeGrenadeProjectile::m_bSmokeEffectSpawned      = offsets.get( "m_bSmokeEffectSpawned" );

    // std::cout << "Offset for m_AttributeManager: 0x" << std::hex << Offset::EconEntity::AttributeManager << std::dec << std::endl;

    std::cout << "[+] Client offsets updated." << std::endl;

  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update client offsets: " << e.what() << std::endl;
    // Optional: Hier k�nntest du entscheiden, das Programm zu beenden oder Standardwerte zu verwenden
  }
}

void useOffsets( const Offsets& offsets ) {
  try {

    Offset::dwPlantedC4                           = offsets.get( "dwPlantedC4" );
    Offset::dwEntityList                          = offsets.get( "dwEntityList" );
    Offset::dwGameEntitySystem                    = offsets.get( "dwGameEntitySystem" );
    Offset::dwGameEntitySystem_highestEntityIndex = offsets.get( "dwGameEntitySystem_highestEntityIndex" );
    Offset::dwViewMatrix                          = offsets.get( "dwViewMatrix" ); 
    Offset::dwLocalPlayerController               = offsets.get( "dwLocalPlayerController" );
    Offset::dwLocalPlayerPawn                     = offsets.get( "dwLocalPlayerPawn" );
    Offset::dwGlobalVars                          = offsets.get( "dwGlobalVars" );
    Offset::dwViewAngles                          = offsets.get( "dwViewAngles" );

    // std::cout << "Offset for dwLocalPlayerController: 0x" << std::hex << Offset::LocalPlayerController << std::dec << std::endl;
    std::cout << "[+] Core offsets updated." << std::endl;

  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update core offsets: " << e.what() << std::endl;
  }
}

void usebuttonOffsets( const Offsets& offsets ) {
  try {
    // Stelle sicher, dass die Namen hier exakt mit den Namen in der heruntergeladenen buttons.hpp �bereinstimmen
    Offset::ForceJump    = offsets.get( "jump" );
    Offset::ForceCrouch  = offsets.get( "duck" );
    Offset::ForceForward = offsets.get( "forward" );    // Pr�fe, ob "forward" der korrekte Key ist
    Offset::ForceLeft    = offsets.get( "left" );   // Pr�fe, ob "moveleft" der korrekte Key ist
    Offset::ForceRight   = offsets.get( "right" );  // Pr�fe, ob "moveright" der korrekte Key ist
    // F�ge hier weitere Buttons hinzu, falls sie in offsets.hpp definiert sind
    // Beispiel: Offset::ForceAttack = offsets.get("attack");

    // std::cout << "Offset for jump: 0x" << std::hex << Offset::ForceJump << std::dec << std::endl;
    std::cout << "[+] Button offsets updated." << std::endl;

  } catch ( const std::exception& e ) {
    std::cerr << "[-] Failed to update button offsets: " << e.what() << std::endl;
  }
}