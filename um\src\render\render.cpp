#include "pch.h"

#include <Windows.h>
#include <iostream>
#include <string>
#include <vector>
#include <cmath>
#include <algorithm>
#include "../../imgui/imgui.h"
#include "render.hpp"
#include "../window/window.hpp"

namespace Render {

  void AddRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness ) {
    ImGui::GetBackgroundDrawList()->AddRect( ImVec2( x, y ), ImVec2( w, h ), ImColor( color ), rounding, 0, thickness );
  }
  void fAddRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness ) {
    ImGui::GetForegroundDrawList()->AddRect( ImVec2( x, y ), ImVec2( w, h ), ImColor( color ), rounding, 0, thickness );
  }

  void DrawRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness ) {
    ImGui::GetBackgroundDrawList()->AddRect( ImVec2( x, y ), ImVec2( x + w, y + h ), ImColor( color ), rounding, 0, thickness );
  }
  void fDrawRect( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness ) {
    ImGui::GetForegroundDrawList()->AddRect( ImVec2( x, y ), ImVec2( x + w, y + h ), ImColor( color ), rounding, 0, thickness );
  }

  void DrawRectFilled( float x, float y, float w, float h, ImVec4 color, float rounding) {
    ImGui::GetBackgroundDrawList()->AddRectFilled( ImVec2( x, y ), ImVec2( x + w, y + h ), ImColor( color ), rounding, 0);
  }

  void DrawRectFilledMultiColor( float x, float y, float w, float h, ImVec4 color, ImVec4 color2 ) {
    ImGui::GetBackgroundDrawList()->AddRectFilledMultiColor( ImVec2( x, y ), ImVec2( x + w, y + h ), ImColor( color ), ImColor( color ), ImColor( color2 ), ImColor( color2 ) );
  }

  void DrawRectFilledMultiColorLandR( float x, float y, float w, float h, ImVec4 color, ImVec4 color2 ) {
    ImGui::GetBackgroundDrawList()->AddRectFilledMultiColor( ImVec2( x, y ), ImVec2( x + w, y + h ), ImColor( color2 ), ImColor( color ), ImColor( color ), ImColor( color2 ) );
  }

  void Filled( float x, float y, float w, float h, ImVec4 color, float rounding, float thickness ) {
    ImGui::GetBackgroundDrawList()->AddRectFilled( ImVec2( x, y ), ImVec2( w, h ), ImColor( color ), rounding, 0 );
  }

  void Line( float x1, float y1, float x2, float y2, ImVec4 color, float thickness ) {
    ImGui::GetBackgroundDrawList()->AddLine( ImVec2( x1, y1 ), ImVec2( x2, y2 ), ImColor( color ), thickness );
  }

  void fLine( float x1, float y1, float x2, float y2, ImVec4 color, float thickness ) {
    ImGui::GetForegroundDrawList()->AddLine( ImVec2( x1, y1 ), ImVec2( x2, y2 ), ImColor( color ), thickness );
  }

  void Circle( float x, float y, float radius, ImVec4 color, float thickness ) {
    ImGui::GetBackgroundDrawList()->AddCircle( ImVec2( x, y ), radius, ImColor( color ), 0, thickness );
  }

  void Dot( float x, float y, float radius, ImVec4 color ) {
    ImGui::GetBackgroundDrawList()->AddCircleFilled( ImVec2( x, y ), radius, ImColor( color ), 0 );
  }
  void fDot( float x, float y, float radius, ImVec4 color ) {
    ImGui::GetForegroundDrawList()->AddCircleFilled( ImVec2( x, y ), radius, ImColor( color ), 0 );
  }

  void Gun(float x, float y, ImVec4 color, std::string Text, float fontSize, int flag) {
    auto RoundToPixel = [](float value) -> float {
      return std::round(value);
      };

    x = RoundToPixel(x);
    y = RoundToPixel(y);

    if (flag == font_flags_t::dropshadow) {
      ImGui::GetBackgroundDrawList()->AddText(
        ImGui::GetFont(), fontSize, ImVec2(x + 1.f, y + 1.f), ImColor(0, 0, 0, 255), Text.c_str());
    }

    if (flag == font_flags_t::outline) {
      // Draw the outline first
      const ImVec2 offsets[] = {
        ImVec2(-1,  0), // Left
        ImVec2( 1,  0), // Right
        ImVec2( 0, -1), // Up
        ImVec2( 0,  1)  // Down
      };

      // Use the same alpha as the main color for the outline
      ImVec4 outlineColor = ImVec4(0.0f, 0.0f, 0.0f, color.w);

      for (const auto& offset : offsets) {
        ImGui::GetBackgroundDrawList()->AddText(
          ImGui::GetFont(), fontSize, ImVec2(x + offset.x, y + offset.y), ImColor(outlineColor), Text.c_str());
      }
    }

    // Draw the main text on top to avoid overlap issues
    ImGui::GetBackgroundDrawList()->AddText( ImGui::GetFont(), fontSize, ImVec2(x, y), ImColor(color), Text.c_str());
  }

  void AALine(float x1, float y1, float x2, float y2, ImVec4 color, float thickness) {
    // Save the original flags
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    // Enable anti-aliasing for this specific draw call
    drawList->Flags |= ImDrawListFlags_AntiAliasedLines;

    // Call the line drawing function
    drawList->AddLine(ImVec2(x1, y1), ImVec2(x2, y2), ImColor(color), thickness);

    // Restore original flags
    drawList->Flags = originalFlags;
  }

  void AACircle(float x, float y, float radius, ImVec4 color, float thickness) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    drawList->Flags |= ImDrawListFlags_AntiAliasedLines;

    drawList->AddCircle(ImVec2(x, y), radius, ImColor(color), 0, thickness);

    drawList->Flags = originalFlags;
  }

  void AADot(float x, float y, float radius, ImVec4 color) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    drawList->Flags |= ImDrawListFlags_AntiAliasedLines | ImDrawListFlags_AntiAliasedFill;

    drawList->AddCircleFilled(ImVec2(x, y), radius, ImColor(color), 0);

    drawList->Flags = originalFlags;
  }

  void Text(float x, float y, ImVec4 color, std::string Text, float fontSize, int flag) {
    auto RoundToPixel = [](float value) -> float {
      return std::round(value);
      };

    x = RoundToPixel(x);
    y = RoundToPixel(y);

    if (flag == font_flags_t::dropshadow) {
      ImGui::GetBackgroundDrawList()->AddText(
        espfont, fontSize, ImVec2(x + 1.f, y + 1.f), ImColor(0, 0, 0, 255), Text.c_str());
    }

    if (flag == font_flags_t::outline) {
      // Draw the outline first
      const ImVec2 offsets[] = {
        ImVec2(-1,  0), // Left
        ImVec2( 1,  0), // Right
        ImVec2( 0, -1), // Up
        ImVec2( 0,  1)  // Down
      };

      // Use the same alpha as the main color for the outline
      ImVec4 outlineColor = ImVec4(0.0f, 0.0f, 0.0f, color.w);

      for (const auto& offset : offsets) {
        ImGui::GetBackgroundDrawList()->AddText(
          espfont, fontSize, ImVec2(x + offset.x, y + offset.y), ImColor(outlineColor), Text.c_str());
      }
    }

    // Draw the main text on top to avoid overlap issues
    ImGui::GetBackgroundDrawList()->AddText(espfont, fontSize, ImVec2(x, y), ImColor(color), Text.c_str());
  }

  void DrawRectGlow(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    // Enable anti-aliasing for smoother glow effect
    drawList->Flags |= ImDrawListFlags_AntiAliasedLines | ImDrawListFlags_AntiAliasedFill;

    // Verbesserter Glow-Effekt mit stärkerer Transparenzabnahme nach außen
    const int numLayers = 6; // Weniger Schichten für einen klareren Effekt mit Anti-Aliasing

    // Von außen nach innen zeichnen
    for (int i = 0; i < numLayers; i++) {
      // Nicht-lineare Abnahme der Transparenz für einen schöneren Effekt
      float t = (float)i / (float)(numLayers - 1); // 0 = außen, 1 = innen

      // Berechne die Größe dieser Schicht (größer am Anfang, kleiner am Ende)
      float layerSize = glowSize * (1.0f - t);

      // Berechne Alpha mit quadratischer Kurve für stärkere Abnahme nach außen
      float alpha = glowColor.w * (t * t); // Quadratische Kurve für schnellere Abnahme

      // Erstelle Farbe mit berechneter Transparenz
      ImVec4 layerColor = ImVec4(glowColor.x, glowColor.y, glowColor.z, alpha);

      // Zeichne die Glow-Schicht
      drawList->AddRectFilled(
        ImVec2(x - layerSize, y - layerSize),
        ImVec2(x + w + layerSize, y + h + layerSize),
        ImColor(layerColor),
        rounding + layerSize / 2.0f, 0);
    }

    // Draw the inner rectangle with the specified color
    drawList->AddRectFilled(
      ImVec2(x, y),
      ImVec2(x + w, y + h),
      ImColor(innerColor),
      rounding, 0);

    // Restore original flags
    drawList->Flags = originalFlags;
  }

  void fDrawRectGlow(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding) {
    ImDrawList* drawList = ImGui::GetForegroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    // Enable anti-aliasing for smoother glow effect
    drawList->Flags |= ImDrawListFlags_AntiAliasedLines | ImDrawListFlags_AntiAliasedFill;

    // Verbesserter Glow-Effekt mit stärkerer Transparenzabnahme nach außen
    const int numLayers = 6; // Weniger Schichten für einen klareren Effekt mit Anti-Aliasing

    // Von außen nach innen zeichnen
    for (int i = 0; i < numLayers; i++) {
      // Nicht-lineare Abnahme der Transparenz für einen schöneren Effekt
      float t = (float)i / (float)(numLayers - 1); // 0 = außen, 1 = innen

      // Berechne die Größe dieser Schicht (größer am Anfang, kleiner am Ende)
      float layerSize = glowSize * (1.0f - t);

      // Berechne Alpha mit quadratischer Kurve für stärkere Abnahme nach außen
      float alpha = glowColor.w * (t * t); // Quadratische Kurve für schnellere Abnahme

      // Erstelle Farbe mit berechneter Transparenz
      ImVec4 layerColor = ImVec4(glowColor.x, glowColor.y, glowColor.z, alpha);

      // Draw the glow layer
      drawList->AddRectFilled(
        ImVec2(x - layerSize, y - layerSize),
        ImVec2(x + w + layerSize, y + h + layerSize),
        ImColor(layerColor),
        rounding + layerSize / 2.0f, 0);
    }

    // Draw the inner rectangle with the specified color
    drawList->AddRectFilled(
      ImVec2(x, y),
      ImVec2(x + w, y + h),
      ImColor(innerColor),
      rounding, 0);

    // Restore original flags
    drawList->Flags = originalFlags;
  }

  void DrawRectGlowMenu(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    // Enable anti-aliasing for smoother glow effect
    drawList->Flags |= ImDrawListFlags_AntiAliasedLines | ImDrawListFlags_AntiAliasedFill;

    // Verbesserter Glow-Effekt mit stärkerer Transparenzabnahme nach außen
    const int numLayers = 20; // Weniger Schichten für einen klareren Effekt mit Anti-Aliasing

    // Von außen nach innen zeichnen
    for (int i = 0; i < numLayers; i++) {
      // Nicht-lineare Abnahme der Transparenz für einen schöneren Effekt
      float t = (float)i / (float)(numLayers - 1); // 0 = außen, 1 = innen

      // Berechne die Größe dieser Schicht (größer am Anfang, kleiner am Ende)
      float layerSize = glowSize * (1.0f - t);

      // Berechne Alpha mit quadratischer Kurve für stärkere Abnahme nach außen
      float alpha = glowColor.w * (t * t); // Quadratische Kurve für schnellere Abnahme

      // Erstelle Farbe mit berechneter Transparenz
      ImVec4 layerColor = ImVec4(glowColor.x, glowColor.y, glowColor.z, alpha);

      // Zeichne die Glow-Schicht
      drawList->AddRectFilled(
        ImVec2(x - layerSize, y - layerSize),
        ImVec2(x + w + layerSize, y + h + layerSize),
        ImColor(layerColor),
        rounding + layerSize / 2.0f, 0);
    }

    // Draw the inner rectangle with the specified color
    drawList->AddRectFilled(
      ImVec2(x, y),
      ImVec2(x + w, y + h),
      ImColor(innerColor),
      rounding, 0);

    // Restore original flags
    drawList->Flags = originalFlags;
  }

  // Animierte Glow-Effekte mit zeitbasierter Transparenz
  void DrawRectGlowAnimated(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding, float pulseSpeed, float minAlpha, float maxAlpha) {
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    // Enable anti-aliasing for smoother glow effect
    drawList->Flags |= ImDrawListFlags_AntiAliasedLines | ImDrawListFlags_AntiAliasedFill;

    // Berechne den aktuellen Alpha-Wert basierend auf der Zeit
    static float startTime = ImGui::GetTime();
    float currentTime = ImGui::GetTime();
    float timeDiff = currentTime - startTime;

    // Sinus-Welle für pulsierenden Effekt (Werte zwischen 0 und 1)
    float pulse = (sinf(timeDiff * pulseSpeed) + 1.0f) * 0.5f;

    // Interpoliere zwischen minAlpha und maxAlpha basierend auf dem Puls
    float currentMaxAlpha = minAlpha + (maxAlpha - minAlpha) * pulse;

    // Anpassen der Glow-Farbe mit dem aktuellen Alpha-Wert
    ImVec4 currentGlowColor = ImVec4(glowColor.x, glowColor.y, glowColor.z, glowColor.w * currentMaxAlpha);

    // Draw multiple layers from outside to inside with exponential falloff for more realistic glow
    const int numLayers = 15; // More layers for smoother gradient

    // Draw from outside to inside for better layering
    for (int i = 0; i < numLayers; i++) {
      // Use exponential falloff for more realistic glow
      float t = (float)i / (float)(numLayers - 1);
      // Exponential curve for more concentrated glow near the object
      float falloff = expf(-t * t * 3.0f);

      // Calculate size for this layer (larger at the beginning, smaller at the end)
      float layerSize = glowSize * (1.0f - t);

      // Calculate alpha based on falloff curve and current max alpha
      float alpha = currentGlowColor.w * falloff;

      // Create color with calculated alpha
      ImVec4 layerColor = ImVec4(currentGlowColor.x, currentGlowColor.y, currentGlowColor.z, alpha);

      // Draw the glow layer
      drawList->AddRectFilled(
        ImVec2(x - layerSize, y - layerSize),
        ImVec2(x + w + layerSize, y + h + layerSize),
        ImColor(layerColor),
        rounding + layerSize / 2.0f, 0);
    }

    // Draw the inner rectangle with the specified color
    drawList->AddRectFilled(
      ImVec2(x, y),
      ImVec2(x + w, y + h),
      ImColor(innerColor),
      rounding, 0);

    // Restore original flags
    drawList->Flags = originalFlags;
  }

  void fDrawRectGlowAnimated(float x, float y, float w, float h, ImVec4 innerColor, ImVec4 glowColor, float glowSize, float rounding, float pulseSpeed, float minAlpha, float maxAlpha) {
    ImDrawList* drawList = ImGui::GetForegroundDrawList();
    ImDrawListFlags originalFlags = drawList->Flags;

    // Enable anti-aliasing for smoother glow effect
    drawList->Flags |= ImDrawListFlags_AntiAliasedLines | ImDrawListFlags_AntiAliasedFill;

    // Berechne den aktuellen Alpha-Wert basierend auf der Zeit
    static float startTime = ImGui::GetTime();
    float currentTime = ImGui::GetTime();
    float timeDiff = currentTime - startTime;

    // Sinus-Welle für pulsierenden Effekt (Werte zwischen 0 und 1)
    float pulse = (sinf(timeDiff * pulseSpeed) + 1.0f) * 0.5f;

    // Interpoliere zwischen minAlpha und maxAlpha basierend auf dem Puls
    float currentMaxAlpha = minAlpha + (maxAlpha - minAlpha) * pulse;

    // Anpassen der Glow-Farbe mit dem aktuellen Alpha-Wert
    ImVec4 currentGlowColor = ImVec4(glowColor.x, glowColor.y, glowColor.z, glowColor.w * currentMaxAlpha);

    // Draw multiple layers from outside to inside with exponential falloff for more realistic glow
    const int numLayers = 15; // More layers for smoother gradient

    // Draw from outside to inside for better layering
    for (int i = 0; i < numLayers; i++) {
      // Use exponential falloff for more realistic glow
      float t = (float)i / (float)(numLayers - 1);
      // Exponential curve for more concentrated glow near the object
      float falloff = expf(-t * t * 3.0f);

      // Calculate size for this layer (larger at the beginning, smaller at the end)
      float layerSize = glowSize * (1.0f - t);

      // Calculate alpha based on falloff curve and current max alpha
      float alpha = currentGlowColor.w * falloff;

      // Create color with calculated alpha
      ImVec4 layerColor = ImVec4(currentGlowColor.x, currentGlowColor.y, currentGlowColor.z, alpha);

      // Draw the glow layer
      drawList->AddRectFilled(
        ImVec2(x - layerSize, y - layerSize),
        ImVec2(x + w + layerSize, y + h + layerSize),
        ImColor(layerColor),
        rounding + layerSize / 2.0f, 0);
    }

    // Draw the inner rectangle with the specified color
    drawList->AddRectFilled(
      ImVec2(x, y),
      ImVec2(x + w, y + h),
      ImColor(innerColor),
      rounding, 0);

    // Restore original flags
    drawList->Flags = originalFlags;
  }

} // namespace Render
