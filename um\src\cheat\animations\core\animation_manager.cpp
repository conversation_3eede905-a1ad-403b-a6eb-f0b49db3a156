#include "pch.h"
#include "animation_manager.hpp"
#include "../../globals.hpp"
#include "../../bones.hpp"
#include "../../gamevars.hpp"
#include "../../../driver/driver.hpp"

// Static member definitions
std::unordered_map<int, std::unique_ptr<DeathAnimationData>> AnimationManager::deathAnimations;
std::unordered_map<int, std::unique_ptr<BarAnimationData>> AnimationManager::healthAnimations;
std::unordered_map<int, std::unique_ptr<BarAnimationData>> AnimationManager::armorAnimations;
std::unordered_map<std::string, std::unique_ptr<UIAnimationData>> AnimationManager::uiAnimations;
std::unordered_map<std::string, std::unique_ptr<ColorAnimationData>> AnimationManager::colorAnimations;

// ===========================
// AUTOMATIC REGISTRATION API
// ===========================

void AnimationManager::RegisterDeathFade(int entityId, const Vector& position, const Vector& eyePosition,
                                        uint64_t boneArray, const std::string& playerName, uint32_t playerFlags,
                                        uint16_t weaponIndex, int health, int armor, int team, bool wasSpotted,
                                        float duration) {
    auto deathData = new DeathAnimationData(position, eyePosition, boneArray, playerName, playerFlags,
                                           weaponIndex, health, armor, team, wasSpotted, duration);
    deathAnimations[entityId] = std::unique_ptr<DeathAnimationData>(deathData);

    // Capture bone positions immediately
    CaptureBonePositions(entityId, boneArray);
}

void AnimationManager::RegisterHealthBar(int entityId, float currentHealth, float targetHealth) {
    // Use default duration from globals or 0.3f as fallback
    float duration = 0.3f; // Smooth health bar animation

    auto healthData = new BarAnimationData(entityId, currentHealth, targetHealth, duration, AnimationType::HEALTH_BAR);
    healthAnimations[entityId] = std::unique_ptr<BarAnimationData>(healthData);
}

void AnimationManager::RegisterArmorBar(int entityId, float currentArmor, float targetArmor) {
    // Use default duration from globals or 0.3f as fallback
    float duration = 0.3f; // Smooth armor bar animation

    auto armorData = new BarAnimationData(entityId, currentArmor, targetArmor, duration, AnimationType::ARMOR_BAR);
    armorAnimations[entityId] = std::unique_ptr<BarAnimationData>(armorData);
}

void AnimationManager::RegisterUIFade(const std::string& elementId, float startAlpha, float targetAlpha, float duration) {
    auto uiData = new UIAnimationData(elementId, startAlpha, targetAlpha, duration, AnimationType::UI_FADE);
    uiAnimations[elementId] = std::unique_ptr<UIAnimationData>(uiData);
}

void AnimationManager::RegisterUISlide(const std::string& elementId, float startPos, float targetPos, float duration) {
    auto uiData = new UIAnimationData(elementId, startPos, targetPos, duration, AnimationType::UI_SLIDE);
    uiAnimations[elementId] = std::unique_ptr<UIAnimationData>(uiData);
}

void AnimationManager::RegisterDelayedUIFade(const std::string& elementId, float startAlpha, float targetAlpha, float duration, float delay) {
    auto uiData = new UIAnimationData(elementId, startAlpha, targetAlpha, duration, AnimationType::UI_FADE, delay);
    uiAnimations[elementId] = std::unique_ptr<UIAnimationData>(uiData);
}

void AnimationManager::RegisterColorTransition(const std::string& elementId, const ImVec4& startColor, const ImVec4& targetColor, float duration, float delay) {
    auto colorData = new ColorAnimationData(elementId, startColor, targetColor, duration, delay);
    colorAnimations[elementId] = std::unique_ptr<ColorAnimationData>(colorData);
}

// ===========================
// QUERY FUNCTIONS
// ===========================

float AnimationManager::GetDeathFadeAlpha(int entityId) {
    auto it = deathAnimations.find(entityId);
    if (it == deathAnimations.end() || !it->second) return 0.0f;

    return AnimationUtils::CalculateFadeAlpha(*it->second);
}

float AnimationManager::GetHealthBarValue(int entityId) {
    auto it = healthAnimations.find(entityId);
    if (it == healthAnimations.end() || !it->second) return 100.0f;

    return AnimationUtils::CalculateBarValue(*it->second);
}

float AnimationManager::GetArmorBarValue(int entityId) {
    auto it = armorAnimations.find(entityId);
    if (it == armorAnimations.end() || !it->second) return 100.0f;

    return AnimationUtils::CalculateBarValue(*it->second);
}

float AnimationManager::GetUIAnimationValue(const std::string& elementId) {
    auto it = uiAnimations.find(elementId);
    if (it == uiAnimations.end() || !it->second) return 1.0f;

    // Use specialized UI progress calculation that handles delays
    float progress = AnimationUtils::CalculateUIProgress(*it->second);
    return AnimationUtils::InterpolateValue(*it->second, progress, AnimationUtils::Easing::EaseInOutQuad);
}

ImVec4 AnimationManager::GetColorAnimationValue(const std::string& elementId) {
    auto it = colorAnimations.find(elementId);
    if (it == colorAnimations.end() || !it->second) return ImVec4(0.0f, 0.0f, 0.0f, 1.0f);

    return AnimationUtils::CalculateColorValue(*it->second);
}

bool AnimationManager::HasActiveDeathAnimation(int entityId) {
    auto it = deathAnimations.find(entityId);
    if (it == deathAnimations.end() || !it->second) return false;

    return it->second->isActive && !AnimationUtils::IsAnimationComplete(*it->second);
}

bool AnimationManager::HasActiveHealthAnimation(int entityId) {
    auto it = healthAnimations.find(entityId);
    if (it == healthAnimations.end() || !it->second) return false;

    return it->second->isActive && !AnimationUtils::IsAnimationComplete(*it->second);
}

bool AnimationManager::HasActiveArmorAnimation(int entityId) {
    auto it = armorAnimations.find(entityId);
    if (it == armorAnimations.end() || !it->second) return false;

    return it->second->isActive && !AnimationUtils::IsAnimationComplete(*it->second);
}

bool AnimationManager::HasActiveUIAnimation(const std::string& elementId) {
    auto it = uiAnimations.find(elementId);
    if (it == uiAnimations.end() || !it->second) return false;

    return it->second->isActive && !AnimationUtils::IsAnimationComplete(*it->second);
}

bool AnimationManager::HasActiveColorAnimation(const std::string& elementId) {
    auto it = colorAnimations.find(elementId);
    if (it == colorAnimations.end() || !it->second) return false;

    return it->second->isActive && !AnimationUtils::IsAnimationComplete(*it->second);
}

bool AnimationManager::HasUIAnimationEntry(const std::string& elementId) {
    auto it = uiAnimations.find(elementId);
    return it != uiAnimations.end() && it->second != nullptr;
}

DeathAnimationData* AnimationManager::GetDeathAnimationData(int entityId) {
    auto it = deathAnimations.find(entityId);
    if (it != deathAnimations.end() && it->second && HasActiveDeathAnimation(entityId)) {
        return it->second.get();
    }
    return nullptr;
}

// ===========================
// MANAGEMENT FUNCTIONS
// ===========================

void AnimationManager::Update() {
    CleanupExpiredDeathAnimations();
    CleanupExpiredBarAnimations();
    CleanupExpiredUIAnimations();
    CleanupExpiredColorAnimations();
}

void AnimationManager::CleanupExpiredDeathAnimations() {
    auto it = deathAnimations.begin();
    while (it != deathAnimations.end()) {
        if (!it->second || AnimationUtils::IsAnimationComplete(*it->second)) {
            it = deathAnimations.erase(it);
        } else {
            ++it;
        }
    }
}

void AnimationManager::CleanupExpiredBarAnimations() {
    auto healthIt = healthAnimations.begin();
    while (healthIt != healthAnimations.end()) {
        if (!healthIt->second || AnimationUtils::IsAnimationComplete(*healthIt->second)) {
            healthIt = healthAnimations.erase(healthIt);
        } else {
            ++healthIt;
        }
    }

    auto armorIt = armorAnimations.begin();
    while (armorIt != armorAnimations.end()) {
        if (!armorIt->second || AnimationUtils::IsAnimationComplete(*armorIt->second)) {
            armorIt = armorAnimations.erase(armorIt);
        } else {
            ++armorIt;
        }
    }
}

void AnimationManager::CleanupExpiredUIAnimations() {
    auto it = uiAnimations.begin();
    while (it != uiAnimations.end()) {
        if (!it->second) {
            it = uiAnimations.erase(it);
        } else {
            // Mark completion if needed (non-const version)
            AnimationUtils::IsAnimationComplete(*it->second);

            // Only cleanup if grace period has elapsed
            if (AnimationUtils::ShouldCleanupAnimation(*it->second)) {
                it = uiAnimations.erase(it);
            } else {
                ++it;
            }
        }
    }
}

void AnimationManager::CleanupExpiredColorAnimations() {
    auto it = colorAnimations.begin();
    while (it != colorAnimations.end()) {
        if (!it->second || AnimationUtils::IsAnimationComplete(*it->second)) {
            it = colorAnimations.erase(it);
        } else {
            ++it;
        }
    }
}

void AnimationManager::ClearAll() {
    deathAnimations.clear();
    healthAnimations.clear();
    armorAnimations.clear();
    uiAnimations.clear();
    colorAnimations.clear();
}

void AnimationManager::ClearDeathAnimations() {
    deathAnimations.clear();
}

void AnimationManager::ClearBarAnimations() {
    healthAnimations.clear();
    armorAnimations.clear();
}

void AnimationManager::ClearUIAnimations() {
    uiAnimations.clear();
}

void AnimationManager::ClearColorAnimations() {
    colorAnimations.clear();
}

void AnimationManager::RemovePlayerAnimations(int entityId) {
    deathAnimations.erase(entityId);
    healthAnimations.erase(entityId);
    armorAnimations.erase(entityId);
}

// ===========================
// SPECIALIZED FUNCTIONS
// ===========================

void AnimationManager::CaptureBonePositions(int entityId, uint64_t boneArray) {
    auto it = deathAnimations.find(entityId);
    if (it == deathAnimations.end() || !it->second) return;

    auto* gameDriver = GameVars::getInstance()->getDriver();
    if (!gameDriver) return;

    // Capture all bone positions for skeleton rendering
    for (const auto& bonePoint : bPoints) {
        Vector bonePos = driver::read_memory<Vector>(gameDriver, boneArray + (bonePoint.bone * 32));
        it->second->frozenBones[bonePoint.bone] = bonePos;
    }
}
