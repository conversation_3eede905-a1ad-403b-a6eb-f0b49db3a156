#pragma once
#include <Windows.h>
#include <string>
#include <vector>

// Declare driver management functions and external variables.
void          createDriver();
int           kdmap( const int argc, wchar_t** argv );
extern HANDLE iqvw64e_device_handle;

// Crash handler and callback functions
LONG WINAPI SimplestCrashHandler( EXCEPTION_POINTERS* ExceptionInfo );
bool        callbackEx( ULONG64* param1, ULONG64* param2, ULONG64 allocationPtr, ULONG64 allocationSize );
