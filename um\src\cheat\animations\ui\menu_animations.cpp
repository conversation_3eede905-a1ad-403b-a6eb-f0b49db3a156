#include "pch.h"
#include "menu_animations.hpp"

// Initialize static member
MenuAnimations::MenuAnimationState MenuAnimations::currentAnimationState = MenuAnimations::MenuAnimationState::IDLE;

// ===========================
// FADE ANIMATIONS
// ===========================

void MenuAnimations::RegisterFadeIn(const std::string& elementId, float duration) {
    RegisterUIAnimation(elementId, 0.0f, 1.0f, duration, AnimationType::UI_FADE);
}

void MenuAnimations::RegisterFadeOut(const std::string& elementId, float duration) {
    RegisterUIAnimation(elementId, 1.0f, 0.0f, duration, AnimationType::UI_FADE);
}

float MenuAnimations::GetFadeAlpha(const std::string& elementId) {
    return AnimationManager::GetUIAnimationValue(elementId);
}

// ===========================
// SLIDE ANIMATIONS
// ===========================

void MenuAnimations::RegisterSlideIn(const std::string& elementId, float startPos, float endPos, float duration) {
    RegisterUIAnimation(elementId, startPos, endPos, duration, AnimationType::UI_SLIDE);
}

void MenuAnimations::RegisterSlideOut(const std::string& elementId, float startPos, float endPos, float duration) {
    RegisterUIAnimation(elementId, startPos, endPos, duration, AnimationType::UI_SLIDE);
}

float MenuAnimations::GetSlidePosition(const std::string& elementId) {
    return AnimationManager::GetUIAnimationValue(elementId);
}

// ===========================
// TRANSITION ANIMATIONS
// ===========================

void MenuAnimations::RegisterTransition(const std::string& elementId, float startValue, float endValue, float duration) {
    RegisterUIAnimation(elementId, startValue, endValue, duration, AnimationType::MENU_TRANSITION);
}

float MenuAnimations::GetTransitionValue(const std::string& elementId) {
    return AnimationManager::GetUIAnimationValue(elementId);
}

// ===========================
// HOVER COLOR ANIMATIONS
// ===========================

void MenuAnimations::RegisterHoverColorIn(const std::string& elementId, const ImVec4& normalColor, const ImVec4& hoverColor, float duration) {
    AnimationManager::RegisterColorTransition(elementId, normalColor, hoverColor, duration, 0.0f);
}

void MenuAnimations::RegisterHoverColorOut(const std::string& elementId, const ImVec4& hoverColor, const ImVec4& normalColor, float duration) {
    AnimationManager::RegisterColorTransition(elementId, hoverColor, normalColor, duration, 0.0f);
}

ImVec4 MenuAnimations::GetHoverColor(const std::string& elementId, const ImVec4& defaultColor) {
    if (AnimationManager::HasActiveColorAnimation(elementId)) {
        return AnimationManager::GetColorAnimationValue(elementId);
    }
    return defaultColor;
}

// ===========================
// HOVER STATE MANAGEMENT
// ===========================

ImVec4 MenuAnimations::HandleButtonHover(const std::string& elementId, bool isHovered, const ImVec4& normalColor, const ImVec4& hoverColor) {
    static std::unordered_map<std::string, bool> lastHoverStates;

    // Check if hover state changed
    bool wasHovered = lastHoverStates[elementId];

    if (isHovered && !wasHovered) {
        // Started hovering - animate to hover color
        RegisterHoverColorIn(elementId, normalColor, hoverColor, 0.4f); // Langsamere Animation
    } else if (!isHovered && wasHovered) {
        // Stopped hovering - animate back to normal color
        RegisterHoverColorOut(elementId, hoverColor, normalColor, 0.35f); // Langsamere Animation
    }

    // Update hover state
    lastHoverStates[elementId] = isHovered;

    // Return the current animated color
    return GetHoverColor(elementId, isHovered ? hoverColor : normalColor);
}

// ===========================
// MENU ELEMENT FADE ANIMATIONS
// ===========================

void MenuAnimations::RegisterElementFadeIn(const std::string& elementId, float delay, float duration) {
    // If delay is specified, register a delayed fade animation
    if (delay > 0.0f) {
        RegisterDelayedFadeIn(elementId, delay, duration);
    } else {
        RegisterUIAnimation(elementId, 0.0f, 1.0f, duration, AnimationType::UI_FADE);
    }
}

void MenuAnimations::RegisterElementFadeOut(const std::string& elementId, float duration) {
    RegisterUIAnimation(elementId, 1.0f, 0.0f, duration, AnimationType::UI_FADE);
}

float MenuAnimations::GetElementFadeAlpha(const std::string& elementId) {
    return AnimationManager::GetUIAnimationValue(elementId);
}

// ===========================
// BOX CONTENT FADE ANIMATIONS
// ===========================

void MenuAnimations::RegisterBoxContentFade(const std::string& boxId, float delay, float duration) {
    std::string contentId = boxId + "_content";
    RegisterElementFadeIn(contentId, delay, duration);
}

float MenuAnimations::GetBoxContentAlpha(const std::string& boxId) {
    std::string contentId = boxId + "_content";
    return GetElementFadeAlpha(contentId);
}

// ===========================
// DELAYED ANIMATION SYSTEM
// ===========================

void MenuAnimations::RegisterDelayedFadeIn(const std::string& elementId, float delay, float duration) {
    // Use the new delayed animation system
    AnimationManager::RegisterDelayedUIFade(elementId, 0.0f, 1.0f, duration, delay);
}

void MenuAnimations::StartBoxAnimationSequence(const std::string& boxId) {
    // Start the box content fade animation with a delay after the box slide animation
    float slideDelay = 0.6f; // Wait for box slide to mostly complete
    RegisterBoxContentFade(boxId, slideDelay, 0.5f);
}

// ===========================
// MENU OPEN/CLOSE ANIMATIONS
// ===========================

void MenuAnimations::StartMenuOpenAnimation(float duration) {
    // Set animation state to opening
    currentAnimationState = MenuAnimationState::OPENING;

    // Register fade in animation for the entire menu
    RegisterUIAnimation("MENU_MASTER", 0.0f, 1.0f, duration, AnimationType::UI_FADE);
}

void MenuAnimations::StartMenuCloseAnimation(float duration) {
    // Set animation state to closing
    currentAnimationState = MenuAnimationState::CLOSING;

    // Register fade out animation for the entire menu
    RegisterUIAnimation("MENU_MASTER", 1.0f, 0.0f, duration, AnimationType::UI_FADE);
}

float MenuAnimations::GetMenuAlpha(bool menuShouldBeVisible) {
    // Get the current alpha value for the menu if animation is active
    if (AnimationManager::HasActiveUIAnimation("MENU_MASTER")) {
        return AnimationManager::GetUIAnimationValue("MENU_MASTER");
    }

    // If no animation is active, return the target state
    return menuShouldBeVisible ? 1.0f : 0.0f;
}

bool MenuAnimations::IsMenuAnimating() {
    bool isAnimating = AnimationManager::HasActiveUIAnimation("MENU_MASTER");

    // If animation is no longer active, reset state to IDLE
    if (!isAnimating && currentAnimationState != MenuAnimationState::IDLE) {
        currentAnimationState = MenuAnimationState::IDLE;
    }

    return isAnimating;
}

// ===========================
// ANIMATION STATE BLOCKING
// ===========================

bool MenuAnimations::IsMenuOpening() {
    // Update animation state first
    IsMenuAnimating();
    return currentAnimationState == MenuAnimationState::OPENING;
}

bool MenuAnimations::IsMenuClosing() {
    // Update animation state first
    IsMenuAnimating();
    return currentAnimationState == MenuAnimationState::CLOSING;
}

bool MenuAnimations::CanToggleMenu() {
    // Update animation state first
    IsMenuAnimating();

    // Menu can only be toggled when no animation is running
    return currentAnimationState == MenuAnimationState::IDLE;
}

// ===========================
// COLORPICKER POPUP ANIMATIONS
// ===========================

void MenuAnimations::RegisterColorPickerPopupOpen(const std::string& popupId, float duration) {
    std::string animationId = "colorpicker_popup_" + popupId;
    RegisterUIAnimation(animationId, 0.0f, 1.0f, duration, AnimationType::UI_FADE);
}

void MenuAnimations::RegisterColorPickerPopupClose(const std::string& popupId, float duration) {
    std::string animationId = "colorpicker_popup_" + popupId;
    RegisterUIAnimation(animationId, 1.0f, 0.0f, duration, AnimationType::UI_FADE);
}

float MenuAnimations::GetColorPickerPopupAlpha(const std::string& popupId) {
    std::string animationId = "colorpicker_popup_" + popupId;
    float alpha = AnimationManager::GetUIAnimationValue(animationId);

    // Ensure we have a minimum alpha for visibility when no animation is active
    if (!IsAnimationActive(animationId)) {
        return 1.0f;
    }

    return alpha;
}

void MenuAnimations::RegisterColorSelectionTransition(const std::string& elementId, const ImVec4& fromColor, const ImVec4& toColor, float duration) {
    std::string animationId = "colorselection_" + elementId;
    AnimationManager::RegisterColorTransition(animationId, fromColor, toColor, duration, 0.0f);
}

ImVec4 MenuAnimations::GetColorSelectionValue(const std::string& elementId, const ImVec4& defaultColor) {
    std::string animationId = "colorselection_" + elementId;

    // If no animation is active, return the default color
    if (!IsAnimationActive(animationId)) {
        return defaultColor;
    }

    return AnimationManager::GetColorAnimationValue(animationId);
}

// ===========================
// CHECKBOX ANIMATIONS
// ===========================

void MenuAnimations::RegisterCheckboxHoverIn(const std::string& checkboxId, float duration) {
    std::string animationId = "checkbox_hover_" + checkboxId;

    // Get current alpha value to start from (avoid circular dependency)
    float currentAlpha = 0.0f;
    if (HasAnimationEntry(animationId)) {
        currentAlpha = AnimationManager::GetUIAnimationValue(animationId);
    } else {
        currentAlpha = GetCheckboxHoverState(checkboxId) ? 1.0f : 0.0f;
    }

    RegisterUIAnimation(animationId, currentAlpha, 1.0f, duration, AnimationType::UI_FADE);
}

void MenuAnimations::RegisterCheckboxHoverOut(const std::string& checkboxId, float duration) {
    std::string animationId = "checkbox_hover_" + checkboxId;

    // Get current alpha value to start from (avoid circular dependency)
    float currentAlpha = 1.0f;
    if (HasAnimationEntry(animationId)) {
        currentAlpha = AnimationManager::GetUIAnimationValue(animationId);
    } else {
        currentAlpha = GetCheckboxHoverState(checkboxId) ? 1.0f : 0.0f;
    }

    RegisterUIAnimation(animationId, currentAlpha, 0.0f, duration, AnimationType::UI_FADE);
}

void MenuAnimations::RegisterCheckboxStateChange(const std::string& checkboxId, bool newState, float duration) {
    std::string animationId = "checkbox_state_" + checkboxId;

    if (newState) {
        // Checking: animate from 0 to 1
        RegisterUIAnimation(animationId, 0.0f, 1.0f, duration, AnimationType::UI_FADE);
    } else {
        // Unchecking: animate from 1 to 0
        RegisterUIAnimation(animationId, 1.0f, 0.0f, duration, AnimationType::UI_FADE);
    }
}

float MenuAnimations::GetCheckboxHoverAlpha(const std::string& checkboxId) {
    std::string animationId = "checkbox_hover_" + checkboxId;

    // First try to get animation value (this handles both active and completed animations)
    if (HasAnimationEntry(animationId)) {
        return AnimationManager::GetUIAnimationValue(animationId);
    }

    // No animation exists, return the target state based on current hover state
    return GetCheckboxHoverState(checkboxId) ? 1.0f : 0.0f;
}

float MenuAnimations::GetCheckboxCheckmarkAlpha(const std::string& checkboxId) {
    std::string animationId = "checkbox_state_" + checkboxId;

    if (!IsAnimationActive(animationId)) {
        return 1.0f; // Default to fully visible if no animation
    }

    return AnimationManager::GetUIAnimationValue(animationId);
}

// Removed GetCheckboxBackgroundColor - we now use original background with hover overlay

// ===========================
// CHECKBOX STATE TRACKING
// ===========================

// Static map to track hover states for checkboxes
static std::unordered_map<std::string, bool> checkboxHoverStates;

void MenuAnimations::SetCheckboxHoverState(const std::string& checkboxId, bool isHovering) {
    checkboxHoverStates[checkboxId] = isHovering;
}

bool MenuAnimations::GetCheckboxHoverState(const std::string& checkboxId) {
    auto it = checkboxHoverStates.find(checkboxId);
    if (it != checkboxHoverStates.end()) {
        return it->second;
    }
    // Default to not hovering if no state is tracked
    return false;
}

// ===========================
// SLIDER SMOOTH ANIMATIONS
// ===========================

// Static map to track slider target values
static std::unordered_map<std::string, float> sliderTargetValues;

void MenuAnimations::RegisterSliderValueChange(const std::string& sliderId, float currentValue, float targetValue, float duration) {
    std::string animationId = "slider_" + sliderId;

    // Store the target value for later reference
    sliderTargetValues[sliderId] = targetValue;

    // Register smooth animation from current to target
    RegisterUIAnimation(animationId, currentValue, targetValue, duration, AnimationType::UI_SLIDE);
}

float MenuAnimations::GetSliderAnimatedValue(const std::string& sliderId, float currentValue) {
    std::string animationId = "slider_" + sliderId;

    // If animation exists, return animated value
    if (HasAnimationEntry(animationId)) {
        return AnimationManager::GetUIAnimationValue(animationId);
    }

    // No animation, return current value
    return currentValue;
}

bool MenuAnimations::IsSliderAnimating(const std::string& sliderId) {
    std::string animationId = "slider_" + sliderId;
    return AnimationManager::HasActiveUIAnimation(animationId);
}

// ===========================
// SEPARATOR ANIMATIONS
// ===========================

void MenuAnimations::RegisterSeparatorFadeIn(const std::string& separatorId, float duration) {
    std::string animationId = "separator_fade_" + separatorId;
    RegisterUIAnimation(animationId, 0.0f, 1.0f, duration, AnimationType::UI_FADE);
}

float MenuAnimations::GetSeparatorFadeProgress(const std::string& separatorId) {
    std::string animationId = "separator_fade_" + separatorId;

    // Always return the current animation value for smooth fade effect
    return AnimationManager::GetUIAnimationValue(animationId);
}

// ===========================
// UTILITY FUNCTIONS
// ===========================

bool MenuAnimations::IsAnimationActive(const std::string& elementId) {
    return AnimationManager::HasActiveUIAnimation(elementId);
}

bool MenuAnimations::HasAnimationEntry(const std::string& elementId) {
    return AnimationManager::HasUIAnimationEntry(elementId);
}

void MenuAnimations::ClearAllMenuAnimations() {
    AnimationManager::ClearUIAnimations();
    // Reset animation state when clearing all animations
    currentAnimationState = MenuAnimationState::IDLE;
}

// ===========================
// INTERNAL HELPERS
// ===========================

void MenuAnimations::RegisterUIAnimation(const std::string& elementId, float startValue, float endValue,
                                        float duration, AnimationType type) {
    if (type == AnimationType::UI_FADE) {
        AnimationManager::RegisterUIFade(elementId, startValue, endValue, duration);
    } else if (type == AnimationType::UI_SLIDE) {
        AnimationManager::RegisterUISlide(elementId, startValue, endValue, duration);
    } else {
        // For other types, use the generic UI fade registration
        AnimationManager::RegisterUIFade(elementId, startValue, endValue, duration);
    }
}
