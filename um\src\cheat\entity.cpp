﻿#include "pch.h"

#include "entity.hpp"
#include "gamevars.hpp"
#include "offsets.hpp"


void Reader::ThreadPlayers() {
  // Wait for complete initialization
  while ( !GameVars::getInstance()->initialized() ) {
    std::this_thread::sleep_for( std::chrono::milliseconds( 100 ) );
  }

  auto vars = GameVars::getInstance();
  std::cout << "[+] Thread Initialization complete" << std::endl;
  //std::cout << "[Thread] Process ID: 0x" << std::hex << vars->getProcessId() << std::endl;
  //std::cout << "[Thread] Client: 0x" << std::hex << vars->getClient() << std::endl;
  //std::cout << "[Thread] Driver: 0x" << std::hex << vars->getDriver() << std::endl;

  while ( true ) {
    FilterPlayers();
    std::this_thread::sleep_for( std::chrono::milliseconds( 5 ) );
  }
}

void Reader::FilterPlayers() {
  std::vector<C_CSPlayerPawn> newPlayerList;
  std::vector<std::string> spectatorList; // Collect spectators

  auto* gameVars = GameVars::getInstance();
  HANDLE driverHandle = gameVars->getDriver();
  uintptr_t clientBase = gameVars->getClient();

  if ( !driverHandle ) {
    std::cout << "[-] Driver handle is null" << std::endl;
    return;
  }

  if ( !clientBase ) {
    std::cout << "[-] Client base is null" << std::endl;
    return;
  }

  uintptr_t entityList  = driver::read_memory<uintptr_t>( driverHandle, clientBase + Offset::dwEntityList );
  uintptr_t localPawn   = driver::read_memory<uintptr_t>( driverHandle, clientBase + Offset::dwLocalPlayerPawn );
  uintptr_t localPlayer = driver::read_memory<uintptr_t>( driverHandle, clientBase + Offset::dwLocalPlayerController );

  uint32_t localPlayerPawnHandle = driver::read_memory<uint32_t>(driverHandle, localPlayer + Offset::PlayerController::m_hPawn);

  // Use a fixed stride to iterate the entity list.
  for ( int playerIndex = 0; playerIndex < 64; playerIndex++ ) {
    uintptr_t listentry  = driver::read_memory<uintptr_t>( driverHandle, entityList + ( ( 8 * ( playerIndex & 0x7FFF ) >> 9 ) + 16 ) );
    uintptr_t player     = driver::read_memory<uintptr_t>( driverHandle, listentry + 120 * ( playerIndex & 0x1FF ) );

    if ( player == localPlayer )
      continue;

    C_CSPlayerPawn CSPlayerPawn;

    std::string PlayerName = driver::read_string( driverHandle, player + Offset::Entity::m_iszPlayerName );
    CSPlayerPawn.PlayerName = PlayerName;

    uint32_t  playerPawn = driver::read_memory<uint32_t>( driverHandle, player + Offset::PlayerController::m_hPawn );

    if (!playerPawn)
      continue;

    uintptr_t listentry2       = driver::read_memory<uintptr_t>( driverHandle, entityList + 0x8 * ( ( playerPawn & 0x7FFF ) >> 9 ) + 16 );
    uintptr_t pCSPlayerPawnPtr = driver::read_memory<uintptr_t>( driverHandle, listentry2 + 120 * ( playerPawn & 0x1FF ) );
    CSPlayerPawn.pCSPlayerPawn = pCSPlayerPawnPtr;
    CSPlayerPawn.entityId = playerIndex;

    if ( !pCSPlayerPawnPtr )
      continue;

    // Testing offsets:
    //std::cout << "m_pObserverServices: 0x" << std::hex << Offset::PlayerController::m_pObserverServices << std::endl;
    //std::cout << "m_hObserverTarget: 0x" << std::hex << Offset::PlayerController::m_hObserverTarget << std::endl;
    // Check if this player is spectating someone
    uintptr_t m_pObserverServices = driver::read_memory<uintptr_t>(driverHandle, pCSPlayerPawnPtr + Offset::PlayerController::m_pObserverServices);
    uint32_t m_hObserverTarget = driver::read_memory<uint32_t>(driverHandle, m_pObserverServices + Offset::PlayerController::m_hObserverTarget);

    if (m_hObserverTarget == localPlayerPawnHandle) {
      // Add to spectator list
      spectatorList.push_back(PlayerName);
      CSPlayerPawn.spectator = PlayerName;
    }

    int health = driver::read_memory<int>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::m_iHealth);
    CSPlayerPawn.health = health;

    if (health < 1 || health > 100)
      continue;

    int armor = driver::read_memory<int>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::m_ArmorValue);
    CSPlayerPawn.armor = armor;

    uintptr_t team = driver::read_memory<uintptr_t>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::m_iTeamNum);
    CSPlayerPawn.team = team;

    bool PlayerSpotted = driver::read_memory<bool>(GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::m_entitySpottedState + Offset::Pawn::m_bSpottedByMask);
    CSPlayerPawn.PlayerSpotted = PlayerSpotted;

    uint32_t PlayerFlags = driver::read_memory<uint32_t>( GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::m_fFlags );
    CSPlayerPawn.PlayerFlags   = PlayerFlags;

    uint64_t gamescene = driver::read_memory<uint64_t>( GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::m_pGameSceneNode );
    uint64_t bonearray = driver::read_memory<uint64_t>( GameVars::getInstance()->getDriver(), gamescene + Offset::Pawn::BoneArray + 0x80 );
    CSPlayerPawn.BoneArray = bonearray;

    uintptr_t ActiveWeapon = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), pCSPlayerPawnPtr + Offset::Pawn::m_pClippingWeapon );

    uint16_t  ItemDefinitionIndex = driver::read_memory<uint16_t>(
      GameVars::getInstance()->getDriver(), ActiveWeapon + Offset::EconEntity::m_AttributeManager
      + Offset::WeaponBaseData::m_Item + Offset::WeaponBaseData::m_iItemDefinitionIndex
    );

    CSPlayerPawn.ItemDefinitionIndex = ItemDefinitionIndex;

    //std::cout << "PlayerSpotted: " << PlayerSpotted << std::endl;                                                             |
    //std::cout << "Offset::Pawn::pClippingWeapon:"<< Offset::Pawn::pClippingWeapon << std::endl;                               |
    //std::cout << "Offset::EconEntity::AttributeManager:"<< Offset::EconEntity::AttributeManager << std::endl;                 |
    //std::cout << "Offset::WeaponBaseData::Item:"<< Offset::WeaponBaseData::Item << std::endl;                                 | Testing
    //std::cout << "Offset::WeaponBaseData::ItemDefinitionIndex:" << Offset::WeaponBaseData::ItemDefinitionIndex << std::endl;  |
    //std::cout << ItemDefinitionIndex << std::endl;                                                                            |

    newPlayerList.push_back( CSPlayerPawn );
  }

  /*
  // Debug: Print how many players were found
  static int debugCounter = 0;
  debugCounter++;
  if (debugCounter % 100 == 0) { // Print every 100 calls
    std::cout << "[Reader Debug] Found " << newPlayerList.size() << " valid players" << std::endl;
  }
  */

  // Atomically update the list
  {
    std::lock_guard<std::mutex> lock( playerListMutex );
    playerList = std::move( newPlayerList );
  }

  // Update global spectator list
  SpectatorData::updateSpectatorList(spectatorList);
}

void Reader::ThreadEntitys() {
  // Wait for complete initialization
  while ( !GameVars::getInstance()->initialized() ) {
    std::this_thread::sleep_for( std::chrono::milliseconds( 100 ) );
  }

  while ( true ) {
    FilterEntitys();
    std::this_thread::sleep_for( std::chrono::milliseconds( 5 ) );
  }
}

void Reader::FilterEntitys() {
  std::vector<C_BaseEntity> newEnitityList;

  uintptr_t entity_List = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), GameVars::getInstance()->getClient() + Offset::dwEntityList );  

  int GameEntitySystem   = driver::read_memory<int>( GameVars::getInstance()->getDriver(), GameVars::getInstance()->getClient() + Offset::dwGameEntitySystem );
  int HighestEntityIndex = driver::read_memory<int>( GameVars::getInstance()->getDriver(), GameEntitySystem + Offset::dwGameEntitySystem_highestEntityIndex );

  //std::cout << "[+] Highest Entity Index: " << std::dec << HighestEntityIndex << std::endl;

  // Use a fixed stride to iterate the entity list.
  for ( int i = 65; i < HighestEntityIndex + 1024; i++ ) {
    uintptr_t list_entry = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), entity_List + 8 * ( ( i & 0x7FFF ) >> 9 ) + 0x10 );

    if ( !list_entry )
      continue;

    uintptr_t BaseEntity = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), list_entry + 120 * ( i & 0x1FF ) );

    if ( !BaseEntity )
      continue;

    uintptr_t pCEntityIdentity = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), BaseEntity + 0x10 );

    if ( !pCEntityIdentity )
      continue;

    uintptr_t classNameAddr = driver::read_memory<uintptr_t>( GameVars::getInstance()->getDriver(), pCEntityIdentity + 0x20 );

    if ( !classNameAddr )
      continue;

    std::string className = driver::read_string( GameVars::getInstance()->getDriver(), classNameAddr );

    C_BaseEntity current_entity;

    if ( className.find( "_projectile" ) == std::string::npos )
      continue;

    //std::cout << "projectile index: " << i << std::endl;

    current_entity.BaseEntity = BaseEntity;
    current_entity.className = className;

    newEnitityList.push_back( current_entity );
  }

  // Atomically update the list
  {
    std::lock_guard<std::mutex> lock( entityListMutex );
    entityList = std::move( newEnitityList );
  }
}

// ===========================
// C_CSPlayerPawn METHODS
// ===========================

Vector C_CSPlayerPawn::getBonePosition(int boneIndex) const {
    if (!BoneArray) {
        return Vector{0, 0, 0};
    }

    auto* gameVars = GameVars::getInstance();
    if (!gameVars || !gameVars->initialized()) {
        return Vector{0, 0, 0};
    }

    // Use the constant from LegitbotConstants instead of magic number 32
    constexpr int BONE_OFFSET_MULTIPLIER = 32;
    return driver::read_memory<Vector>(
        gameVars->getDriver(),
        BoneArray + boneIndex * BONE_OFFSET_MULTIPLIER
    );
}

