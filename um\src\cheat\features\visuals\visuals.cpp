#include "pch.h"
#include "visuals.hpp"
#include "../../gamedata/items.hpp"
#include "../../globals.hpp"
#include "../../../utils/utils.hpp"
#include "../../gamedata.hpp"
#include "../../../window/window.hpp"

// Global variable definitions
HANDLE g_driver = nullptr;
uintptr_t g_client = 0;

using namespace globals;

void VISUALS::InitializeGlobals() {
  g_driver = GameVars::getInstance()->getDriver();
  g_client = GameVars::getInstance()->getClient();
}

// Projectile tracking state (file scope)
namespace {
  std::unordered_map<uintptr_t, std::pair<Vector, int>> projectilePositionTracker; // last position + static count
  std::unordered_set<uintptr_t> bannedProjectiles;                                 // permanently banned projectiles
  std::unordered_map<uintptr_t, std::vector<Vector>> projectileTrajectories;       // stored world positions
  std::unordered_map<uintptr_t, std::chrono::steady_clock::time_point> trajectoryStartTime; // erase timers
  constexpr int MAX_STATIC_COUNT = 100;
  Vector g_projectileScreenPos{};
  std::string g_projectileName{};
}

// Temporary structure for tracking player data (for death detection) - RESTORED
struct DeathPlayerData {
    Vector worldPosition;
    Vector eyeWorldPosition;
    uint64_t boneArray;
    std::string playerName;
    uint32_t playerFlags;
    uint16_t weaponIndex;
    int health;
    int armor;
    int team;
    bool wasSpotted;

    DeathPlayerData() = default;

    DeathPlayerData(const Vector& pos, const Vector& eyePos, uint64_t bones, const std::string& name,
                   uint32_t flags, uint16_t weapon, int hp, int ar, int tm, bool spotted)
        : worldPosition(pos), eyeWorldPosition(eyePos), boneArray(bones), playerName(name),
          playerFlags(flags), weaponIndex(weapon), health(hp), armor(ar),
          team(tm), wasSpotted(spotted) {}
};

void VISUALS::RenderESP( const Reader& reader ) {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  auto playerList = reader.getPlayerListCopy();
  auto entityList = reader.getEntityListCopy();

  ImGui::GetStyle().AntiAliasedLines = false;
  ImGui::GetStyle().AntiAliasedFill = false;
  ImGui::GetStyle().AntiAliasedLinesUseTex = false;

  if (DarkMode::enabled) {
    Darkmode();
  }

  // CLEAN: Use GameData convenience wrapper system
  int localTeam = GameData::getLocalTeam();
  const view_matrix_t viewMatrix = GameData::getViewMatrix();

  // Track current alive players and register deaths
  static std::unordered_map<int, DeathPlayerData> lastKnownPlayerData;
  std::unordered_set<int> currentlyAlive;

  // First pass: collect currently alive players and update their data
  for ( const auto& player : playerList ) {
    currentlyAlive.insert(player.entityId);

    Vector playerWorldOrigin = GameData::getPlayerPosition(player.pCSPlayerPawn);
    Vector playerViewOffset = driver::read_memory<Vector>(g_driver, player.pCSPlayerPawn + Offset::Pawn::m_vecViewOffset);
    Vector playerEyeWorldPos = playerWorldOrigin;
    playerEyeWorldPos.z += playerViewOffset.z;

    lastKnownPlayerData[player.entityId] = DeathPlayerData(
      playerWorldOrigin, playerEyeWorldPos, player.BoneArray, player.PlayerName,
      player.PlayerFlags, player.ItemDefinitionIndex,
      player.health, player.armor, player.team, player.PlayerSpotted
    );
  }

  // Check for players that are no longer alive and register deaths
  if (globals::Esp::Death::enabled) {
    for (auto it = lastKnownPlayerData.begin(); it != lastKnownPlayerData.end();) {
      if (currentlyAlive.find(it->first) == currentlyAlive.end()) {
        // Player is no longer in the alive list - register death using new system
        DeathAnimations::RegisterPlayerDeath
        (
          it->first,
          it->second.worldPosition,
          it->second.eyeWorldPosition,
          it->second.boneArray,
          it->second.playerName,
          it->second.playerFlags,
          it->second.weaponIndex,
          it->second.health,
          it->second.armor,
          it->second.team,
          it->second.wasSpotted
        );

        it = lastKnownPlayerData.erase(it);
      } else {
        ++it;
      }
    }
  }

  for ( const auto& player : playerList ) {
    // Check if player is enemy or teammate
    const bool isCurrentPlayerEnemy = (player.team != localTeam);

    // Skip teammates if "Ignore Teammates" is enabled
    if (!isCurrentPlayerEnemy && Esp::ignoreTeammates) continue;

    // Set spotted drawing colors based on player spotted state
    bool spotted = player.PlayerSpotted;
    Esp::Box::Spotted::drawingColor      = ( spotted && Esp::Box::Spotted::enabled ) ? Esp::Box::Spotted::Color : Esp::Box::Color;
    Esp::Skeleton::Spotted::drawingColor = ( spotted && Esp::Skeleton::Spotted::enabled ) ? Esp::Skeleton::Spotted::Color : Esp::Skeleton::Color;
    Esp::Snapline::Spotted::drawingColor = ( spotted && Esp::Snapline::Spotted::enabled ) ? Esp::Snapline::Spotted::Color : Esp::Snapline::Color;
    Esp::Info::Spotted::drawingColor     = ( spotted && Esp::Info::Spotted::enabled ) ? Esp::Info::Spotted::Color : Esp::Info::Color;
    Esp::Viewline::Spotted::drawingColor = ( spotted && Esp::Viewline::Spotted::enabled ) ? Esp::Viewline::Spotted::Color : Esp::Viewline::Color;

    Vector playerWorldOrigin = GameData::getPlayerPosition(player.pCSPlayerPawn);
    Vector playerViewOffset = driver::read_memory<Vector>(g_driver, player.pCSPlayerPawn + Offset::Pawn::m_vecViewOffset);
    Vector playerWorldHeadPos = playerWorldOrigin;
    playerWorldHeadPos.z += playerViewOffset.z + 9;

    Vector screenHeadPos, screenFeetPos;
    if ( Vector::world_to_screen( viewMatrix, playerWorldHeadPos, screenHeadPos ) &&
      Vector::world_to_screen( viewMatrix, playerWorldOrigin, screenFeetPos ) ) {

      const float boxHeight = screenFeetPos.y - screenHeadPos.y;
      const float boxHalfWidth  = boxHeight * 0.25f;

      // Set global variables for current player - cleaner approach
      CurrentPlayer::screenHead = screenHeadPos;
      CurrentPlayer::screenFeet = screenFeetPos;
      CurrentPlayer::boxHeight = boxHeight;
      CurrentPlayer::boxHalfWidth = boxHalfWidth;
      CurrentPlayer::playerName = player.PlayerName;
      CurrentPlayer::playerFlags = player.PlayerFlags;
      CurrentPlayer::weaponIndex = player.ItemDefinitionIndex;
      CurrentPlayer::health = player.health;
      CurrentPlayer::armor = player.armor;
      CurrentPlayer::entityId = player.entityId;
      CurrentPlayer::boneArray = player.BoneArray;

      // Always render snaplines regardless of culling
      if (Esp::Snapline::enabled) {
        DrawPlayerSnapline();
      }

      // Only render other ESP elements if they are completely on screen

      if (Esp::Health::Bar::enabled) {
        // Register health animation automatically - now with proper value change detection
        HealthAnimations::UpdateHealthAnimation(player.entityId, static_cast<float>(player.health));
        DrawPlayerHealth();
      }

      if (Esp::Armor::Bar::enabled && player.armor > 0) {
        // Register armor animation automatically - now with proper value change detection
        ArmorAnimations::UpdateArmorAnimation(player.entityId, static_cast<float>(player.armor));
        DrawPlayerArmor();
      }

      if (Esp::Box::enabled) {
        if (Esp::Box::type == 0) {
          DrawPlayerBox();
        } else {
          DrawPlayerCorneredBox();
        }
      }

      if (Esp::Box::Filled::enabled) {
        DrawPlayerFilledBox();
      }

      if (Esp::Info::enabled) {
        DrawPlayerInfo();
      }

      if (Esp::Viewline::enabled) {
        Vector playerEyeAngles = driver::read_memory<Vector>( g_driver, player.pCSPlayerPawn + Offset::Pawn::m_angEyeAngles );
        Vector playerEyeWorldPos = playerWorldOrigin;
        playerEyeWorldPos.z += playerViewOffset.z;
        // Set global variables for viewline
        CurrentPlayer::viewAngles = playerEyeAngles;
        CurrentPlayer::eyeWorldPos = playerEyeWorldPos;
        DrawViewline();
      }

      if (Esp::Skeleton::enabled) {
        DrawPlayerSkeleton();

        if (Esp::Skeleton::Dots::enabled) {
          DrawPlayerJoints();
        }

        if (Esp::Skeleton::Head::enabled) {
          DrawPlayerHead();
        }
      }
    }
  }

  // Render death animations using the new clean system
  DeathAnimations::RenderAll(viewMatrix, localTeam);

  // Projectiles and grenades inline pipeline (no parameters in draw fns)
  for (const auto& entity : entityList) {
    if (entity.className.find("_projectile") == std::string::npos) continue;
    if (bannedProjectiles.find(entity.BaseEntity) != bannedProjectiles.end()) continue;

    std::string cleanName = RemoveSuffix(entity.className, "_projectile");
    Vector worldPos = GameData::getEntityPosition(entity.BaseEntity);
    if (worldPos.isInvalid()) continue;

    // tracking
    auto& tracker = projectilePositionTracker[entity.BaseEntity];
    auto& traj = projectileTrajectories[entity.BaseEntity];
    const bool same = tracker.first.x == worldPos.x && tracker.first.y == worldPos.y && tracker.first.z == worldPos.z;
    if (same) {
      tracker.second++;
      if (tracker.second >= MAX_STATIC_COUNT) {
        bannedProjectiles.insert(entity.BaseEntity);
        projectilePositionTracker.erase(entity.BaseEntity);
        if (globals::Projectile::line && globals::Projectile::erase) {
          trajectoryStartTime[entity.BaseEntity] = std::chrono::steady_clock::now();
        } else {
          auto it = projectileTrajectories.find(entity.BaseEntity);
          if (it != projectileTrajectories.end()) it->second.clear();
        }
        continue;
      }
    } else {
      tracker.first = worldPos;
      tracker.second = 0;
      traj.push_back(worldPos);
    }

    // draw
    if (Vector::world_to_screen(viewMatrix, worldPos, g_projectileScreenPos)) {
      g_projectileName = cleanName;
      if (globals::Projectile::enabled) {
        if (globals::Projectile::name) DrawProjectileName();
        if (globals::Projectile::box)  DrawProjectileBox();
      }
    }

    // smoke collect
    if (globals::Smoke::enabled && cleanName == "smokegrenade") {
      bool spawned = driver::read_memory<bool>(g_driver, entity.BaseEntity + Offset::SmokeGrenadeProjectile::m_bSmokeEffectSpawned);
      auto& smokeData = smokeStates[entity.BaseEntity];
      if (spawned && !smokeData.active) {
        smokeData = SmokeData(std::chrono::steady_clock::now(), worldPos);
      }
    }
  }

  // Trajectories
  DrawProjectileTrajectories();

  // Cleanup trackers for missing entities
  auto it = projectilePositionTracker.begin();
  while (it != projectilePositionTracker.end()) {
    const uintptr_t id = it->first;
    bool exists = false;
    for (const auto& e : entityList) { if (e.BaseEntity == id) { exists = true; break; } }
    if (!exists) {
      if (trajectoryStartTime.find(id) == trajectoryStartTime.end()) {
        if (globals::Projectile::line && globals::Projectile::erase) {
          trajectoryStartTime[id] = std::chrono::steady_clock::now();
        } else {
          auto trajIt = projectileTrajectories.find(id);
          if (trajIt != projectileTrajectories.end()) trajIt->second.clear();
        }
      }
      it = projectilePositionTracker.erase(it);
    } else {
      ++it;
    }
  }

  auto trajIt = projectileTrajectories.begin();
  while (trajIt != projectileTrajectories.end()) {
    if (trajIt->second.empty()) {
      trajIt = projectileTrajectories.erase(trajIt);
    } else {
      ++trajIt;
    }
  }

   // Handle smoke rendering
  if ( globals::Smoke::enabled ) {
    DrawSmoke();
  }

  if (globals::C4::enabled) {
    DrawC4();
  }

   // Render aimbot circle
  if ( globals::Legitbot::enabled ) {
    DrawAimbotCircle();
  }

}

void VISUALS::DrawProjectileName() {
  ImGui::PushFont(espfont);
  ImVec2 size = ImGui::CalcTextSize(g_projectileName.c_str());
  ImGui::PopFont();
  const float x = g_projectileScreenPos.x - (size.x * 0.5f);
  const float y = g_projectileScreenPos.y - size.y - 5.0f;
  ImGui::PushFont(espfont);
  Render::Text(x, y, globals::Projectile::Color, g_projectileName, fontSize, font_flags_t::outline);
  ImGui::PopFont();
}

void VISUALS::DrawProjectileBox() {
  constexpr float boxSize = 8.0f;
  Render::DrawRect(
    g_projectileScreenPos.x - boxSize * 0.5f,
    g_projectileScreenPos.y - boxSize * 0.5f,
    boxSize,
    boxSize,
    globals::Projectile::Color,
    0.0f,
    1.0f
  );
}

void VISUALS::DrawProjectileTrajectories() {
  if (!globals::Projectile::line) {
    for (auto it = trajectoryStartTime.begin(); it != trajectoryStartTime.end();) {
      uintptr_t id = it->first;
      auto trajIt = projectileTrajectories.find(id);
      if (trajIt != projectileTrajectories.end()) {
        trajIt->second.clear();
      }
      it = trajectoryStartTime.erase(it);
    }
    return;
  }

  for (auto it = projectileTrajectories.begin(); it != projectileTrajectories.end(); ++it) {
    uintptr_t id = it->first;
    auto& traj = it->second;

    auto startIt = trajectoryStartTime.find(id);
    if (startIt != trajectoryStartTime.end() && globals::Projectile::erase) {
      auto now = std::chrono::steady_clock::now();
      auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(now - startIt->second).count();
      int toRemove = static_cast<int>(elapsedMs / 500);
      if (toRemove > 0 && !traj.empty()) {
        int sz = static_cast<int>(traj.size());
        int rm = toRemove < sz ? toRemove : sz;
        traj.erase(traj.begin(), traj.begin() + rm);
        if (traj.empty()) {
          trajectoryStartTime.erase(startIt);
          continue;
        }
      }
    }

    if (traj.size() >= 2) {
      for (size_t i = 0; i + 1 < traj.size(); ++i) {
        Vector w1 = traj[i];
        Vector w2 = traj[i + 1];
        Vector s1, s2;
        const view_matrix_t vm = GameData::getViewMatrix();
        if (Vector::world_to_screen(vm, w1, s1) && Vector::world_to_screen(vm, w2, s2)) {
          Render::Line(s1.x, s1.y, s2.x, s2.y, globals::Projectile::Color, 1.f);
        }
      }
    }
  }
}

void VISUALS::DrawAimbotCircle() {
 
  if (globals::Legitbot::Circle::Filled::enabled)
    Render::AADot(centerX + 0.5f, centerY + 0.5f, globals::Legitbot::radius - 1.f, globals::Legitbot::Circle::Filled::Color);
 
  if (globals::Legitbot::Circle::enabled)
    Render::AACircle(centerX + 0.5f, centerY + 0.5f, globals::Legitbot::radius, globals::Legitbot::Circle::Color, 1.f);
}

void VISUALS::DrawPlayerBox() {
  const float boxHeight = CurrentPlayer::boxHeight;
  const float boxHalfVisualWidth = CurrentPlayer::boxHalfWidth;

  const float boxLeftEdgeX  = CurrentPlayer::screenHead.x - boxHalfVisualWidth;
  const float boxTopEdgeY   = CurrentPlayer::screenHead.y;
  const float boxTotalVisualWidth = boxHalfVisualWidth * 2.0f;

  const ImVec4 mainBoxColor   = Esp::Box::Spotted::drawingColor;
  ImVec4 outlineBoxColor = {0.f, 0.f, 0.f, 1.f};
  // Make outline fade with the main box color
  outlineBoxColor.w = mainBoxColor.w;

  const float outlineThicknessPx = 1.0f;

  if (Esp::Box::outline) {
    const float outerOutlineX = boxLeftEdgeX - outlineThicknessPx;
    const float outerOutlineY = boxTopEdgeY - outlineThicknessPx;
    const float outerOutlineWidth = boxTotalVisualWidth + (outlineThicknessPx * 2.0f);
    const float outerOutlineHeight = boxHeight + (outlineThicknessPx * 2.0f);
    Render::DrawRect(outerOutlineX, outerOutlineY, outerOutlineWidth, outerOutlineHeight, outlineBoxColor, Esp::Box::rounding, 1.f);

    const float innerOutlineX = boxLeftEdgeX + outlineThicknessPx;
    const float innerOutlineY = boxTopEdgeY + outlineThicknessPx;
    const float innerOutlineWidth = boxTotalVisualWidth - (outlineThicknessPx * 2.0f);
    const float innerOutlineHeight = boxHeight - (outlineThicknessPx * 2.0f);
    Render::DrawRect(innerOutlineX, innerOutlineY, innerOutlineWidth, innerOutlineHeight, outlineBoxColor, Esp::Box::rounding, 1.f);
  }

  Render::DrawRect(boxLeftEdgeX, boxTopEdgeY, boxTotalVisualWidth, boxHeight, mainBoxColor, Esp::Box::rounding, 1.f);
}

void VISUALS::DrawPlayerFilledBox() {
  const float boxHeight = CurrentPlayer::boxHeight;
  const float boxHalfVisualWidth = CurrentPlayer::boxHalfWidth;

  const float boxLeftEdgeX  = CurrentPlayer::screenHead.x - boxHalfVisualWidth;
  const float boxTopEdgeY   = CurrentPlayer::screenHead.y;
  const float boxTotalVisualWidth = boxHalfVisualWidth * 2.0f;

  const ImVec4 topFillColor    = Esp::Box::Filled::Color;
  const ImVec4 bottomFillColor = Esp::Box::Filled::Color2;

  Render::DrawRectFilledMultiColor(
    boxLeftEdgeX,
    boxTopEdgeY,
    boxTotalVisualWidth,
    boxHeight,
    topFillColor,
    bottomFillColor
  );
}

void VISUALS::DrawPlayerCorneredBox() {
  const float boxHeight           = CurrentPlayer::boxHeight;
  const float boxHalfVisualWidth  = CurrentPlayer::boxHalfWidth;
  const float cornerLineLength    = boxHalfVisualWidth * Esp::Box::length;

  const float leftEdgeX   = CurrentPlayer::screenHead.x - boxHalfVisualWidth;
  const float rightEdgeX  = CurrentPlayer::screenHead.x + boxHalfVisualWidth;
  const float topEdgeY    = CurrentPlayer::screenHead.y;
  const float bottomEdgeY = CurrentPlayer::screenHead.y + boxHeight;

  const ImVec4 mainCornerColor  = Esp::Box::Spotted::drawingColor;
  ImVec4 outlineCornerColor = {0.f, 0.f, 0.f, 1.f};
  // Make outline fade with the main corner color
  outlineCornerColor.w = mainCornerColor.w;

  const std::vector<std::pair<int, int>> fixedOutlineOffsets = {
    {-1, -1}, {1, -1}, {-1, 1}, {1, 1}
  };
  const float outlineLineThickness = 1.f;

  if (Esp::Box::outline) {
    for (const auto& offsetPair : fixedOutlineOffsets) {
      const float dx = static_cast<float>(offsetPair.first);
      const float dy = static_cast<float>(offsetPair.second);

      Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(leftEdgeX + dx, topEdgeY + dy, leftEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX - cornerLineLength + dx, topEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(rightEdgeX + dx, topEdgeY + dy, rightEdgeX + dx, topEdgeY + cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(leftEdgeX + dx, bottomEdgeY + dy, leftEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);

      Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX - cornerLineLength + dx, bottomEdgeY + dy, outlineCornerColor, outlineLineThickness);
      Render::Line(rightEdgeX + dx, bottomEdgeY + dy, rightEdgeX + dx, bottomEdgeY - cornerLineLength + dy, outlineCornerColor, outlineLineThickness);
    }
  }

  const float mainLineThickness = 1.f;
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX + cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, topEdgeY, leftEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, topEdgeY, rightEdgeX - cornerLineLength, topEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, topEdgeY, rightEdgeX, topEdgeY + cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX + cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(leftEdgeX, bottomEdgeY, leftEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX - cornerLineLength, bottomEdgeY, mainCornerColor, mainLineThickness);
  Render::Line(rightEdgeX, bottomEdgeY, rightEdgeX, bottomEdgeY - cornerLineLength, mainCornerColor, mainLineThickness);

}

void VISUALS::DrawPlayerHealth() {
  if (Esp::Health::Bar::Style::type == Esp::HealthBarStyle::Solid) {
    DrawPlayerHealthBarSolid();
  } else {
    DrawPlayerHealthBarReactive();
  }
}

void VISUALS::DrawPlayerHealthBarReactive() {
  const float animatedHealth = HealthAnimations::GetAnimatedHealth(CurrentPlayer::entityId, static_cast<float>(CurrentPlayer::health));
  // FIXED: Wrapped std::min and std::max in parentheses to avoid macro conflicts
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = CurrentPlayer::boxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (CurrentPlayer::screenHead.x - CurrentPlayer::boxHalfWidth) - barLeftOffset;
  const float barPositionY = CurrentPlayer::screenHead.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  // For reactive health bar, check if this is a death animation using new AnimationManager
  const bool isDeathAnimation = AnimationManager::HasActiveDeathAnimation(CurrentPlayer::entityId);

  ImVec4 currentBarReactiveColor;
  if (isDeathAnimation) {
    // Use death color with fade alpha for death animations
    currentBarReactiveColor = globals::Esp::Death::Color;
    currentBarReactiveColor.w *= AnimationManager::GetDeathFadeAlpha(CurrentPlayer::entityId);
  } else {
    // Normal reactive color calculation
    currentBarReactiveColor = ImVec4{
      1.0f - healthRatioClamped,
      healthRatioClamped,
      0.0f,
      1.0f  // Normal reactive bar should always be fully opaque
    };
  }
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the health bar
  barBackgroundColor.w = currentBarReactiveColor.w;

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      currentBarReactiveColor.x,
      currentBarReactiveColor.y,
      currentBarReactiveColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      currentBarReactiveColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, currentBarReactiveColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    ImVec4 healthTextColor = Esp::Health::Value::Color;
    // Make health text fade but keep it white
    healthTextColor.w = currentBarReactiveColor.w;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerHealthBarSolid() {
  const float animatedHealth = HealthAnimations::GetAnimatedHealth(CurrentPlayer::entityId, static_cast<float>(CurrentPlayer::health));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float healthRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedHealth / 100.f));

  const float barFullHeight = CurrentPlayer::boxHeight;
  const float filledBarActualHeight = barFullHeight * healthRatioClamped;
  const float barLeftOffset = 5.f;
  const float barPositionX = (CurrentPlayer::screenHead.x - CurrentPlayer::boxHalfWidth) - barLeftOffset;
  const float barPositionY = CurrentPlayer::screenHead.y;
  const float barVisualWidth = 2.f;
  const float backgroundVisualWidth = 4.f;
  const float backgroundBorderOffset = 1.f;

  const std::string healthValueText = std::to_string(static_cast<int>(animatedHealth));

  const ImVec4 solidBarColor = Esp::Health::Bar::Color;
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the solid health bar
  barBackgroundColor.w = solidBarColor.w;

  if (Esp::Health::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      solidBarColor.x,
      solidBarColor.y,
      solidBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barPositionX,
      barPositionY,
      barVisualWidth,
      barFullHeight,
      solidBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barPositionX - backgroundBorderOffset;
  const float bgRectY = barPositionY - backgroundBorderOffset;
  const float bgRectWidth = backgroundVisualWidth;
  const float bgRectHeight = barFullHeight + (backgroundBorderOffset * 2.0f);
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderY = barPositionY + (barFullHeight - filledBarActualHeight);
  Render::DrawRectFilled(barPositionX, filledBarRenderY, barVisualWidth, filledBarActualHeight, solidBarColor, 0);

  if (Esp::Health::Value::enabled && animatedHealth < 100) {
    ImGui::PushFont(espfont);
    const float healthTextHorizontalOffset = (static_cast<int>(animatedHealth) <= 9) ? 3.f : 7.f;
    const float textRenderPosX = barPositionX - healthTextHorizontalOffset + 1.f;
    const float textRenderPosY = filledBarRenderY - 6.f;
    ImVec4 healthTextColor = Esp::Health::Value::Color;
    // Make health text fade but keep it white
    healthTextColor.w = solidBarColor.w;

    Render::Text(textRenderPosX, textRenderPosY, healthTextColor, healthValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerArmor() {
  const float animatedArmor = ArmorAnimations::GetAnimatedArmor(CurrentPlayer::entityId, static_cast<float>(CurrentPlayer::armor));
  // FIXED: Wrapped std::min and std::max in parentheses
  const float armorRatioClamped = (std::min)(1.f, (std::max)(0.f, animatedArmor / 100.f));

  const float barFullVisualWidth = CurrentPlayer::boxHalfWidth * 2.f;
  const float filledBarActualWidth = barFullVisualWidth * armorRatioClamped;
  const float barStartX = CurrentPlayer::screenHead.x - CurrentPlayer::boxHalfWidth;
  const float barTopOffset = 4.f;
  const float barPositionY = CurrentPlayer::screenHead.y + CurrentPlayer::boxHeight + barTopOffset;
  const float barVisualHeight = 2.f;
  const float backgroundVisualHeight = 4.f;
  const float backgroundBorderOffsetY = 1.f;

  const std::string armorValueText = std::to_string(static_cast<int>(animatedArmor));

  const ImVec4 armorBarColor = Esp::Armor::Bar::Color;
  ImVec4 barBackgroundColor = {0.f, 0.f, 0.f, 1.f};
  // Make background fade with the armor bar
  barBackgroundColor.w = armorBarColor.w;

  if (Esp::Armor::Bar::Glow::enabled) {
    const ImVec4 glowEffectColor = {
      armorBarColor.x,
      armorBarColor.y,
      armorBarColor.z,
      0.7f
    };
    const int glowBlurRadius = 7;
    const int glowSpread = 2;
    Render::DrawRectGlow(
      barStartX,
      barPositionY,
      barFullVisualWidth,
      barVisualHeight,
      armorBarColor,
      glowEffectColor,
      glowBlurRadius,
      glowSpread
    );
  }

  const float bgRectX = barStartX;
  const float bgRectY = barPositionY - backgroundBorderOffsetY;
  const float bgRectWidth = barFullVisualWidth + 2.f;
  const float bgRectHeight = backgroundVisualHeight;
  Render::DrawRectFilled(bgRectX, bgRectY, bgRectWidth, bgRectHeight, barBackgroundColor, 0);

  const float filledBarRenderX = barStartX + 1.f;
  Render::DrawRectFilled(filledBarRenderX, barPositionY, filledBarActualWidth, barVisualHeight, armorBarColor, 0);

  if (Esp::Armor::Value::enabled && animatedArmor < 100 && animatedArmor > 0) {
    ImGui::PushFont(espfont);
    const float armorTextHorizontalOffset = 3.f;
    const float textRenderPosX = barStartX + filledBarActualWidth - armorTextHorizontalOffset;
    const float textRenderPosY = CurrentPlayer::screenHead.y + CurrentPlayer::boxHeight + 2.f;
    ImVec4 armorTextColor = Esp::Armor::Value::Color;
    // Make armor text fade but keep it white
    armorTextColor.w = armorBarColor.w;

    Render::Text(textRenderPosX, textRenderPosY, armorTextColor, armorValueText, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::DrawPlayerInfo() {
  const float boxHeight = CurrentPlayer::boxHeight;
  const float boxHalfVisualWidth = CurrentPlayer::boxHalfWidth;

  const char* cstrItemName = GetItemName(static_cast<ItemIndex>(CurrentPlayer::weaponIndex));
  const std::string currentItemName = (cstrItemName == nullptr) ? "N/A" : cstrItemName;
  const std::string weaponIconGlyph = GetItemIcon(static_cast<ItemIndex>(CurrentPlayer::weaponIndex));

  constexpr uint32_t IN_AIR_FLAG_VALUE = (1 << 0);
  const bool isPlayerGrounded = (CurrentPlayer::playerFlags & IN_AIR_FLAG_VALUE) == 0;
  const std::string playerAirGroundState = isPlayerGrounded ? "OnGround" : "InAir";

  // Dynamic positioning system - elements move up when others are disabled
  const float rightSideBaseX = (CurrentPlayer::screenHead.x + boxHalfVisualWidth) + 3.0f;
  const float rightSideBaseY = CurrentPlayer::screenHead.y;

  // Calculate dynamic Y positions for right-side elements (name, state)
  float currentRightSideY = rightSideBaseY;

  const float nameTextPosX = rightSideBaseX;
  const float nameTextPosY = currentRightSideY;
  if(globals::Esp::Info::Name::player) {
    currentRightSideY += 10.0f; // Move down for next element
  }

  const float stateTextPosX = rightSideBaseX;
  const float stateTextPosY = currentRightSideY;
  if(globals::Esp::Info::state) {
    currentRightSideY += 10.0f; // Move down for next element
  }

  // Calculate dynamic Y positions for bottom elements (weapon text, weapon icon)
  const float bottomBaseY = CurrentPlayer::screenHead.y + boxHeight;
  float currentBottomY = bottomBaseY;

  // Check if armor bar is enabled to add spacing
  if(globals::Esp::Armor::Bar::enabled && CurrentPlayer::armor > 0) {
    currentBottomY += 9.0f; // Add armor bar spacing
  } else {
    currentBottomY += 4.0f; // Add minimum spacing when armor bar is disabled
  }

  ImGui::PushFont(espfont);
  const float itemNameTextWidth = ImGui::CalcTextSize(currentItemName.c_str()).x;
  ImGui::PopFont();
  const float itemNameTextBaseX = CurrentPlayer::screenHead.x;
  const float itemNameTextPosX = itemNameTextBaseX - (itemNameTextWidth / 2.0f);
  const float itemNameTextPosY = currentBottomY;
  if(globals::Esp::Info::Name::weapon) {
    currentBottomY += 10.0f; // Move down for weapon icon
  }

  ImGui::PushFont(gunIcons);
  const float weaponIconGlyphWidth = ImGui::CalcTextSize(weaponIconGlyph.c_str()).x;
  ImGui::PopFont();
  const float weaponIconBaseX = CurrentPlayer::screenHead.x;
  const float weaponIconPosX = weaponIconBaseX - (weaponIconGlyphWidth / 2.0f);
  const float weaponIconPosY = currentBottomY;

  const ImVec4 infoElementColor = Esp::Info::Spotted::drawingColor;
  const float defaultFontSize = fontSize;
  const float iconDisplayFontSize = 18.0f;
  const font_flags_t textRenderStyle = font_flags_t::outline;

  ImGui::PushFont(espfont);
  if(globals::Esp::Info::Name::player){
    Render::Text(nameTextPosX, nameTextPosY, infoElementColor, CurrentPlayer::playerName, defaultFontSize, textRenderStyle);
  }
  if(globals::Esp::Info::state){
    Render::Text(stateTextPosX, stateTextPosY, infoElementColor, playerAirGroundState, defaultFontSize, textRenderStyle);
  }
  if (globals::Esp::Info::Name::weapon){
    Render::Text(itemNameTextPosX, itemNameTextPosY, infoElementColor, currentItemName, defaultFontSize, textRenderStyle);
  }
  ImGui::PopFont();

  ImGui::PushFont(gunIcons);
  if(globals::Esp::Info::Icon::enabled){
    Render::Gun(weaponIconPosX, weaponIconPosY, infoElementColor, weaponIconGlyph, iconDisplayFontSize, textRenderStyle);
  }
  ImGui::PopFont();
}

void VISUALS::DrawViewline() {
  const float viewLineRenderLength = Esp::Viewline::length;
  const float viewLineStartOffset = 10.f;
  const float lineRenderThickness = 1.0f;
  const ImVec4 viewLineColor = Esp::Viewline::Spotted::drawingColor;

  const float dotSquareHalfDim = 1.5f;
  const float dotLineRenderThickness = 1.0f;
  const ImVec4 dotSquareColor = Esp::Viewline::FacingBox::Color;

  const float pitchInRadians = CurrentPlayer::viewAngles.x * static_cast<float>(M_PI) / 180.f;
  const float yawInRadians = CurrentPlayer::viewAngles.y * static_cast<float>(M_PI) / 180.f;

  const Vector directionVector = {
    cosf(yawInRadians) * cosf(pitchInRadians),
    sinf(yawInRadians) * cosf(pitchInRadians),
    -sinf(pitchInRadians)
  };

  // FIXED: Removed const from lineWorldStartPos and lineWorldEndPos
  Vector lineWorldStartPos = CurrentPlayer::eyeWorldPos + directionVector * viewLineStartOffset;
  Vector lineWorldEndPos = CurrentPlayer::eyeWorldPos + directionVector * (viewLineStartOffset + viewLineRenderLength);

  Vector lineScreenStartPos, lineScreenEndPos;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, lineWorldStartPos, lineScreenStartPos) &&
    Vector::world_to_screen(viewMatrix, lineWorldEndPos, lineScreenEndPos)) {

    Render::AALine(lineScreenStartPos.x, lineScreenStartPos.y, lineScreenEndPos.x, lineScreenEndPos.y, viewLineColor, lineRenderThickness);

    if (Esp::Viewline::FacingBox::enabled) {
      // FIXED: Reverted to original-style calculation for right/up vectors for the dot, avoiding normalize/cross
      Vector dotOrientationRight = {
        -sinf(yawInRadians),
        cosf(yawInRadians),
        0.f
      };
      Vector dotOrientationUp = {
        cosf(yawInRadians) * sinf(pitchInRadians),
        sinf(yawInRadians) * sinf(pitchInRadians),
        cosf(pitchInRadians)
      };

      Vector worldCornerFTR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFTL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim + dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBR = lineWorldEndPos + dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;
      Vector worldCornerFBL = lineWorldEndPos - dotOrientationRight * dotSquareHalfDim - dotOrientationUp * dotSquareHalfDim;

      Vector screenCornerFTR, screenCornerFTL, screenCornerFBR, screenCornerFBL;
      const bool areDotPointsOnScreen =
        Vector::world_to_screen(viewMatrix, worldCornerFTR, screenCornerFTR) &&
        Vector::world_to_screen(viewMatrix, worldCornerFTL, screenCornerFTL) &&
        Vector::world_to_screen(viewMatrix, worldCornerFBR, screenCornerFBR) &&
        Vector::world_to_screen(viewMatrix, worldCornerFBL, screenCornerFBL);

      if (areDotPointsOnScreen &&
        screenCornerFTR.x != 0.f && screenCornerFTR.y != 0.f && screenCornerFTR.z != 0.f && // Comparing floats to 0.f
        screenCornerFTL.x != 0.f && screenCornerFTL.y != 0.f && screenCornerFTL.z != 0.f &&
        screenCornerFBR.x != 0.f && screenCornerFBR.y != 0.f && screenCornerFBR.z != 0.f &&
        screenCornerFBL.x != 0.f && screenCornerFBL.y != 0.f && screenCornerFBL.z != 0.f) {

        Render::AALine(screenCornerFTL.x, screenCornerFTL.y, screenCornerFTR.x, screenCornerFTR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFTR.x, screenCornerFTR.y, screenCornerFBR.x, screenCornerFBR.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBR.x, screenCornerFBR.y, screenCornerFBL.x, screenCornerFBL.y, dotSquareColor, dotLineRenderThickness);
        Render::AALine(screenCornerFBL.x, screenCornerFBL.y, screenCornerFTL.x, screenCornerFTL.y, dotSquareColor, dotLineRenderThickness);
      }
    }
  }
}

void VISUALS::DrawPlayerSkeleton() {
  const ImVec4 skeletonLineColor = Esp::Skeleton::Spotted::drawingColor;
  const float skeletonLineThickness = 1.0f;
  view_matrix_t viewMatrix = GameData::getViewMatrix();

  constexpr size_t numBoneConnections = sizeof(bConnections) / sizeof(bConnections[0]);
  std::vector<Vector> screenBone1Positions;
  std::vector<Vector> screenBone2Positions;
  screenBone1Positions.reserve(numBoneConnections);
  screenBone2Positions.reserve(numBoneConnections);

  for (const auto& bonePairIndices : bConnections) {
    const uintptr_t bone1MemoryAddress = CurrentPlayer::boneArray + static_cast<uintptr_t>(bonePairIndices.bone1) * 32;
    const uintptr_t bone2MemoryAddress = CurrentPlayer::boneArray + static_cast<uintptr_t>(bonePairIndices.bone2) * 32;

    Vector worldBone1Pos = driver::read_memory<Vector>( g_driver, bone1MemoryAddress );
    Vector worldBone2Pos = driver::read_memory<Vector>( g_driver, bone2MemoryAddress );

    Vector boneSegmentVector = worldBone1Pos - worldBone2Pos;
    float boneLength = sqrtf(
      powf(boneSegmentVector.x, 2.f) +
      powf(boneSegmentVector.y, 2.f) +
      powf(boneSegmentVector.z, 2.f)
    );

    Vector screenBone1Pos, screenBone2Pos;
    if (boneLength < MAX_BONE_LENGTH &&
      boneLength > 0.0f &&
      Vector::world_to_screen(viewMatrix, worldBone1Pos, screenBone1Pos) &&
      Vector::world_to_screen(viewMatrix, worldBone2Pos, screenBone2Pos)) {

      screenBone1Positions.push_back(screenBone1Pos);
      screenBone2Positions.push_back(screenBone2Pos);
    }
  }

  for (size_t i = 0; i < screenBone1Positions.size(); ++i) {
    const Vector& p1OnScreen = screenBone1Positions[i];
    const Vector& p2OnScreen = screenBone2Positions[i];
    Render::AALine(p1OnScreen.x, p1OnScreen.y, p2OnScreen.x, p2OnScreen.y, skeletonLineColor, skeletonLineThickness);
  }
}

void VISUALS::DrawPlayerJoints() {
  ImVec4 jointDotColor = Esp::Skeleton::Dots::Color;
  float configuredRadiusFactor = Esp::Skeleton::Dots::radius;
  view_matrix_t viewMatrix = GameData::getViewMatrix();

  float dynamicBaseRadius = 0.0f;

  std::vector<Vector> screenJointPositions;
  screenJointPositions.reserve(sizeof(bPoints) / sizeof(bPoints[0]));

  // Phase 1: Sammle Gelenk-Bildschirmpositionen
  for (const auto& jointPointDef : bPoints) {
    uintptr_t boneAddr = CurrentPlayer::boneArray + static_cast<uintptr_t>(jointPointDef.bone) * 32;
    Vector worldJointPos = driver::read_memory<Vector>( g_driver, boneAddr );
    Vector screenJointPos;

    if (Vector::world_to_screen(viewMatrix, worldJointPos, screenJointPos)) {
      screenJointPositions.push_back(screenJointPos);
    }

  }

  // Basisradius aus Nacken/Wirbelsäule einmalig bestimmen
  {
    Vector worldNeck = driver::read_memory<Vector>( g_driver, CurrentPlayer::boneArray + bones::neck * 32 );
    Vector worldSpine = driver::read_memory<Vector>( g_driver, CurrentPlayer::boneArray + bones::spine * 32 );
    Vector currentScreenNeck, currentScreenSpine;
    if (Vector::world_to_screen(viewMatrix, worldNeck, currentScreenNeck) &&
        Vector::world_to_screen(viewMatrix, worldSpine, currentScreenSpine)) {
      dynamicBaseRadius = std::abs(currentScreenNeck.y - currentScreenSpine.y);
    }
  }

  // Phase 2: Zeichne alle Gelenkpunkte mit dem final bestimmten dynamicBaseRadius.
  for (const auto& screenPos : screenJointPositions) {
    Render::AADot(screenPos.x, screenPos.y, dynamicBaseRadius * configuredRadiusFactor, jointDotColor);
    if (configuredRadiusFactor <= 0.4f) { // Originale Bedingung f�r den zweiten Punkt
      Render::AADot(screenPos.x, screenPos.y, 0.5f, jointDotColor);
    }
  }
}

void VISUALS::DrawPlayerHead() {
  const uintptr_t headBoneAddr  = CurrentPlayer::boneArray + static_cast<uintptr_t>(bones::head) * 32;
  const uintptr_t neckBoneAddr  = CurrentPlayer::boneArray + static_cast<uintptr_t>(bones::neck) * 32;
  const uintptr_t spineBoneAddr = CurrentPlayer::boneArray + static_cast<uintptr_t>(bones::spine) * 32;

  Vector worldHeadPos  = driver::read_memory<Vector>( g_driver, headBoneAddr );
  Vector worldNeckPos  = driver::read_memory<Vector>( g_driver, neckBoneAddr );
  Vector worldSpinePos = driver::read_memory<Vector>( g_driver, spineBoneAddr );

  Vector screenHead, screenNeck, screenSpine;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, worldHeadPos, screenHead) &&
    Vector::world_to_screen(viewMatrix, worldNeckPos, screenNeck) &&
    Vector::world_to_screen(viewMatrix, worldSpinePos, screenSpine)) {

    const float headCircleRadius = std::abs(screenNeck.y - screenSpine.y);

    const ImVec4 headCircleColor = Esp::Skeleton::Head::Color;
    const float circleLineThickness = 1.0f;

    Render::AACircle(screenHead.x, screenHead.y, headCircleRadius, headCircleColor, circleLineThickness);
  }
}

void VISUALS::DrawPlayerSnapline() {
  const int systemScreenWidth = GetSystemMetrics(SM_CXSCREEN);

  const float lineStartXCoord = static_cast<float>(systemScreenWidth / 2);
  const float lineStartYCoord = 0.f;

  const float lineEndXCoord = CurrentPlayer::screenHead.x;
  const float lineEndYCoord = CurrentPlayer::screenHead.y;

  const ImVec4 snaplineColor = Esp::Snapline::Spotted::drawingColor;
  const float snaplineThickness = Esp::Snapline::thickness;

  Render::AALine(lineStartXCoord, lineStartYCoord, lineEndXCoord, lineEndYCoord, snaplineColor, snaplineThickness);
}

void VISUALS::DrawProjectile() {
  // Check if projectile rendering is enabled
  if (!globals::Projectile::enabled) return;

  // Check if any projectile feature is enabled
  if (!globals::Projectile::name && !globals::Projectile::box) return;

  // Draw the projectile name if enabled
  if (globals::Projectile::name) {
    // Calculate text position above the projectile
    ImGui::PushFont(espfont);
    ImVec2 textSize = ImGui::CalcTextSize(CurrentPlayer::projectileName.c_str());
    ImGui::PopFont();

    float textX = CurrentPlayer::screenProjectilePos.x - (textSize.x / 2.0f);  // Center horizontally
    float textY = CurrentPlayer::screenProjectilePos.y - textSize.y - 5.0f;    // Position above projectile with 5px spacing

    // Draw the projectile name using Render::Text function with ESP font
    ImGui::PushFont(espfont);
    Render::Text(textX, textY, globals::Projectile::Color, CurrentPlayer::projectileName, fontSize, font_flags_t::outline);
    ImGui::PopFont();
  }

  // Draw a small box around projectile if enabled
  if (globals::Projectile::box) {
    float boxSize = 8.0f;
    Render::DrawRect(
      CurrentPlayer::screenProjectilePos.x - boxSize/2,
      CurrentPlayer::screenProjectilePos.y - boxSize/2,
      boxSize,
      boxSize,
      globals::Projectile::Color,
      0.0f,  // rounding
      1.0f   // thickness
    );
  }
}

void VISUALS::DrawSmoke() {
  if ( !g_driver) {
    return;
  }

  // Render ALL active smokes (independent of current entity)
  if (globals::Smoke::enabled) {
    const view_matrix_t viewMatrix = GameData::getViewMatrix();
    
    for (auto it = smokeStates.begin(); it != smokeStates.end();) {
      auto& smokeDat = it->second;
      
      if (!smokeDat.active) {
        ++it;
        continue;
      }
      
      float elapsedTime = std::chrono::duration<float>(std::chrono::steady_clock::now() - smokeDat.startTime).count();
      
      // Check if smoke has expired
      if (elapsedTime >= maxTime) {
        it = smokeStates.erase(it);
        continue;
      }
      
      int remainingTime = static_cast<int>(maxTime - elapsedTime);
      
      Vector smokePos = smokeDat.position;
      smokePos.z += 50.0f;
      
      Vector screenPos;
      if (!Vector::world_to_screen(viewMatrix, smokePos, screenPos)) {
        ++it;
        continue;
      }
      
      // Render smoke elements
      if (globals::Smoke::name::enabled) {
        const std::string smokeText = "SMOKE";
        ImGui::PushFont(espfont);
        const float textWidth = ImGui::CalcTextSize(smokeText.c_str()).x;
        ImGui::PopFont();
        
        const float centeredX = screenPos.x - (textWidth * 0.5f);
        Render::Text(centeredX, screenPos.y - 10, globals::Smoke::name::Color, smokeText, fontSize, font_flags_t::outline);
      }
      
      if (globals::Smoke::circle::enabled) {
        HorizontalCircle(smokeDat.position, 150, 64, viewMatrix, globals::Smoke::circle::Color);
      }
      
      if (globals::Smoke::countdown::enabled && globals::Smoke::countdown::bar::enabled) {
        const float lineWidth = 23.0f;
        const float lineHeight = 2.0f;
        const float currentWidth = lineWidth * (static_cast<float>(remainingTime) / maxTime);
        const float lineStartX = screenPos.x - (lineWidth * 0.5f);
        const float lineStartY = screenPos.y + 15.0f;
        
        // Draw black background (outline)
        Render::DrawRectFilled(lineStartX - 1, lineStartY - 1, lineWidth + 2, lineHeight + 2, ImVec4(0.0f, 0.0f, 0.0f, 1.0f), 0.0f);
        
        // Draw countdown bar centered on the background
        Render::DrawRectFilled(lineStartX, lineStartY, currentWidth, lineHeight, globals::Smoke::countdown::bar::Color, 0.0f);
        
        if (globals::Smoke::countdown::text::enabled) {
          Render::Text(lineStartX + currentWidth, lineStartY - 3, globals::Smoke::countdown::text::Color, std::to_string(remainingTime), fontSize, font_flags_t::outline);
        }
      }
      
      ++it;
    }
  }
}

void VISUALS::DrawC4() {
  const view_matrix_t viewMatrix = GameData::getViewMatrix();
  const DWORD64 c4Planted = driver::read_memory<DWORD64>(g_driver, g_client + Offset::dwPlantedC4 - 0x8);
  
  if (!c4Planted) 
    return;

  const uintptr_t c4 = driver::read_memory<uintptr_t>(g_driver, driver::read_memory<uintptr_t>(g_driver, g_client + Offset::dwPlantedC4));
  const uintptr_t c4Node = driver::read_memory<uintptr_t>(g_driver, c4 + Offset::Pawn::m_pGameSceneNode);
  Vector c4Origin = driver::read_memory<Vector>(g_driver, c4Node + Offset::m_vecAbsOrigin);

  Vector c4ScreenPos;
  if (!Vector::world_to_screen(viewMatrix, c4Origin, c4ScreenPos))
    return;

  const char* c4Text = "C4";
  const std::string c4IconGlyph = GetItemIcon(ItemIndex::WeaponC4);
  ImVec4 c4Color = globals::C4::Color;

  ImGui::PushFont(espfont);
  const ImVec2 textSize = ImGui::CalcTextSize(c4Text);
  ImGui::PopFont();

  ImGui::PushFont(gunIcons);
  const float iconWidth = ImGui::CalcTextSize(c4IconGlyph.c_str()).x;
  ImGui::PopFont();

  const float centeredTextX = c4ScreenPos.x - (textSize.x * 0.5f);
  const float centeredTextY = c4ScreenPos.y - (textSize.y * 0.5f);
  const float centeredIconX = c4ScreenPos.x - (iconWidth * 0.5f);
  const float centeredIconY = c4ScreenPos.y;

  if (globals::C4::name::enabled) {
    Render::Text(centeredTextX, centeredTextY, globals::C4::name::Color, c4Text, fontSize, font_flags_t::outline);
  }

  if (globals::C4::icon::enabled) {
    ImGui::PushFont(gunIcons);
    Render::Gun(centeredIconX, centeredIconY, globals::C4::icon::Color, c4IconGlyph, 18.f, font_flags_t::outline);
    ImGui::PopFont();
  }
}

void VISUALS::Darkmode() {
    Render::DrawRectFilled( 0.f, 0.f, Screen::width, Screen::height, { 0, 0, 0, DarkMode::alpha / 200 }, 0.f );
}
