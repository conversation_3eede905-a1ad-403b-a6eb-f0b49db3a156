#  > What category does the cheat fall under?
```
Nebula.cc is set to become a kernel-level cheat.
```
///////////////////////////////////////////////////////////////////////
#  > How will the cheat access the game's memory? Will it operate at the kernel level?
```
As mentioned, the cheat will operate at the kernel level and solely read the game's memory.
```
#  > Will there be a user interface to control the cheat's features, and if so, what will it look like?
```
Currently, no. Initially, the focus will be on developing all the features, with the subsequent creation of a user-friendly menu, particularly suitable for beginners.
```
///////////////////////////////////////////////////////////////////////
#  > How safe is it ? 
```
As I mentioned before, it's a kernel-level cheat, currently only reading CS2's memory, making it undetectable by CS2's anti-cheat. Of course, you can still get banned due to mass reports and Overwatch reviews, but if you play discreetly, you shouldn't get banned.
```
///////////////////////////////////////////////////////////////////////
#  > Will there be a paid license to use the cheat, and if so, how will the pricing be determined?
```
Yes, it will be paid, but not too much. I haven't finalized the price yet, but I think it will be around 3-5 euros per month.
```
///////////////////////////////////////////////////////////////////////
#  > Are there plans for customer support in case users encounter issues or have questions about the cheat?
```
Yes, there will be customer support available. It will work like this: you create a ticket, describe the issue you're experiencing, and I'll assist you from there.
```
///////////////////////////////////////////////////////////////////////
#  > How will the community be involved in the ongoing development of the cheat, such as providing feedback and suggesting new features?
```
I'll make many decisions myself and consult my community on what can be improved or added. However, I won't implement everything the community suggests.
```
///////////////////////////////////////////////////////////////////////
#  > Can I get a refund?
```
All sales are __***final***__ and non-refundable unless you're unable to use the cheat and can provide proof, but only on the same day of purchase.
```
///////////////////////////////////////////////////////////////////////
#  > When will the cheat be released?
```
I don't have a specific release date.
```
///////////////////////////////////////////////////////////////////////
#  > Will it support Linux or MacOS?
```
No, it will specifically be for Windows 10 and Windows 11 only.
```
///////////////////////////////////////////////////////////////////////
#  > What features will the cheat have?

* Features:
* ESP: Box, Cornered Box, Filled Box, Skeleton, HeadCircle/HeadBox, HealthBar, HealthText & State (scoped/in-air/on-ground/crouched...), and more.
- Aimbot
- TriggerBot
- Bunnyhop (with chance to hit)
- Recoil Control System
- No-scope Crosshair
- Spectator List
- Anti-AFK
- Radar
- and more

///////////////////////////////////////////////////////////////////////
