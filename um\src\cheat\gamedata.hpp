#pragma once

// System includes
#include <windows.h>
#include <cstdint>
#include <chrono>
#include <mutex>

// Project includes
#include "gamevars.hpp"
#include "offsets.hpp"
#include "../driver/driver.hpp"
#include "../math/vector.hpp"

/**
 * @brief GameData - Convenience wrapper system for accessing commonly used CS2 game variables
 *
 * This class provides simplified access to frequently used game data with caching capabilities
 * to reduce the verbosity of memory reading operations throughout the codebase.
 *
 * Usage Examples:
 *   view_matrix_t matrix = GameData::getViewMatrix();
 *   uintptr_t localPlayer = GameData::getLocalPlayerController();
 *   int localTeam = GameData::getLocalTeam();
 *   Vector playerPos = GameData::getPlayerPosition(playerPawn);
 */
class GameData {
public:
    // ===========================
    // CORE GAME DATA ACCESS
    // ===========================

    /**
     * @brief Get the current view matrix for world-to-screen calculations
     * @return view_matrix_t Current view matrix
     */
    static view_matrix_t getViewMatrix();

    /**
     * @brief Get the local player controller pointer
     * @return uintptr_t Local player controller address
     */
    static uintptr_t getLocalPlayerController();

    /**
     * @brief Get the local player pawn pointer
     * @return uintptr_t Local player pawn address
     */
    static uintptr_t getLocalPlayerPawn();

    /**
     * @brief Get the entity list pointer
     * @return uintptr_t Entity list address
     */
    static uintptr_t getEntityList();

    /**
     * @brief Get the local player's team number
     * @return int Team number (2 = Terrorist, 3 = Counter-Terrorist)
     */
    static int getLocalTeam();

    /**
     * @brief Get the current view angles
     * @return Vector Current view angles (pitch, yaw, roll)
     */
    static Vector getViewAngles();

    // ===========================
    // PLAYER DATA ACCESS
    // ===========================

    /**
     * @brief Get player position from pawn pointer
     * @param playerPawn Player pawn address
     * @return Vector Player world position
     */
    static Vector getPlayerPosition(uintptr_t playerPawn);

    /**
     * @brief Get player health from pawn pointer
     * @param playerPawn Player pawn address
     * @return int Player health (0-100)
     */
    static int getPlayerHealth(uintptr_t playerPawn);

    /**
     * @brief Get player armor from pawn pointer
     * @param playerPawn Player pawn address
     * @return int Player armor (0-100)
     */
    static int getPlayerArmor(uintptr_t playerPawn);

    /**
     * @brief Get player team from pawn pointer
     * @param playerPawn Player pawn address
     * @return int Player team number
     */
    static int getPlayerTeam(uintptr_t playerPawn);

    /**
     * @brief Get player flags from pawn pointer
     * @param playerPawn Player pawn address
     * @return uint32_t Player flags bitfield
     */
    static uint32_t getPlayerFlags(uintptr_t playerPawn);

    /**
     * @brief Get player spotted state from pawn pointer
     * @param playerPawn Player pawn address
     * @return bool True if player is spotted
     */
    static bool getPlayerSpotted(uintptr_t playerPawn);

    /**
     * @brief Get the entity ID of the player currently targeted by the crosshair
     * @param localPlayerController Local player controller address
     * @return int Entity ID of the crosshair target
     */
    static int getCrosshairTargetId(uintptr_t localPlayerController);

    /**
     * @brief Get player pawn from entity ID
     * @param entityId Entity ID
     * @return uintptr_t Player pawn address
     */
    static uintptr_t getPlayerPawn(int entityId);

    // ===========================
    // WEAPON DATA ACCESS
    // ===========================

    /**
     * @brief Get active weapon pointer from player pawn
     * @param playerPawn Player pawn address
     * @return uintptr_t Active weapon address
     */
    static uintptr_t getActiveWeapon(uintptr_t playerPawn);

    /**
     * @brief Get weapon item definition index
     * @param weaponPtr Weapon address
     * @return uint16_t Item definition index
     */
    static uint16_t getWeaponItemIndex(uintptr_t weaponPtr);

    /**
     * @brief Get shots fired count from player pawn
     * @param playerPawn Player pawn address
     * @return int Number of shots fired
     */
    static int getShotsFired(uintptr_t playerPawn);

    /**
     * @brief Get scoped state from player pawn
     * @param playerPawn Player pawn address
     * @return bool True if player is scoped
     */
    static bool getIsScoped(uintptr_t playerPawn);

    // ===========================
    // BONE DATA ACCESS
    // ===========================

    /**
     * @brief Get bone array pointer from player pawn
     * @param playerPawn Player pawn address
     * @return uint64_t Bone array address
     */
    static uint64_t getBoneArray(uintptr_t playerPawn);

    /**
     * @brief Get specific bone position
     * @param boneArray Bone array address
     * @param boneIndex Bone index (see bones.hpp)
     * @return Vector Bone world position
     */
    static Vector getBonePosition(uint64_t boneArray, int boneIndex);

    // ===========================
    // ENTITY DATA ACCESS
    // ===========================

    /**
     * @brief Get entity position from base entity
     * @param entityPtr Base entity address
     * @return Vector Entity world position
     */
    static Vector getEntityPosition(uintptr_t entityPtr);

    /**
     * @brief Get entity game scene node
     * @param entityPtr Base entity address
     * @return uintptr_t Game scene node address
     */
    static uintptr_t getEntityGameSceneNode(uintptr_t entityPtr);

    // ===========================
    // UTILITY FUNCTIONS
    // ===========================

    /**
     * @brief Check if GameVars is properly initialized
     * @return bool True if initialized and ready to use
     */
    static bool isInitialized();

    /**
     * @brief Get driver handle (convenience wrapper)
     * @return HANDLE Driver handle
     */
    static HANDLE getDriver();

    /**
     * @brief Get client base address (convenience wrapper)
     * @return uintptr_t Client base address
     */
    static uintptr_t getClientBase();

    // ===========================
    // CACHING SYSTEM (Optional)
    // ===========================

    /**
     * @brief Enable/disable caching for frequently accessed data
     * @param enabled True to enable caching
     */
    static void setCachingEnabled(bool enabled);

    /**
     * @brief Clear all cached data (force refresh)
     */
    static void clearCache();

private:
    // Caching system
    static bool cachingEnabled;
    static std::mutex cacheMutex;

    // Cached data with timestamps
    struct CachedData {
        uintptr_t localPlayerController = 0;
        uintptr_t localPlayerPawn = 0;
        uintptr_t entityList = 0;
        int localTeam = 0;
        std::chrono::steady_clock::time_point lastUpdate;
        bool isValid = false;
    };

    static CachedData cache;
    static constexpr std::chrono::milliseconds CACHE_TIMEOUT{50}; // 50ms cache timeout

    // Helper functions
    static bool isCacheValid();
    static void updateCache();
    static GameVars* getGameVars();
};
