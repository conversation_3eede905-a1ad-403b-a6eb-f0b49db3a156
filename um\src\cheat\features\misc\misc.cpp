#include "pch.h"
#include "misc.hpp"
#include <string>
#include "../../offsets.hpp"
#include "../../gamedata/items.hpp"
#include "../../gamedata.hpp" // GameData convenience wrapper system

void MISC::InitializeGlobals() {
  g_driver = GameVars::getInstance()->getDriver();
  g_client = GameVars::getInstance()->getClient();
}

void MISC::RenderMisc( const Reader& reader ) {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  ImGui::GetStyle().AntiAliasedLines = false;
  ImGui::GetStyle().AntiAliasedFill = false;
  ImGui::GetStyle().AntiAliasedLinesUseTex = false;

  if (globals::Sound::enabled) {
    Sound();
  }

  if (globals::Hitmarker::enabled) {
    Hitmarker();
  }
  if (globals::Misc::RecoilCrosshair::enabled) {
    RecoilCrosshair();
  }
  if (globals::Crosshair::enabled) {
    SniperCrosshair();
  }
  if (globals::Watermark::enabled) {
    Watermark();
  }
}

void MISC::RecoilCrosshair() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // CLEAN: Use GameData convenience wrapper system
  Vector currentViewAngles = GameData::getViewAngles();
  uintptr_t localPlayerPawn = GameData::getLocalPlayerPawn();
  if (!localPlayerPawn) return;

  int numShots = GameData::getShotsFired(localPlayerPawn);

  // Only draw if player is shooting more than 1 bullet
  if (numShots <= 1) return;

  // Get aim punch angle for recoil compensation (this offset might need to be added to GameData in the future)
  Vector aimPunchAngle = driver::read_memory<Vector>( g_driver, localPlayerPawn + Offset::Pawn::m_aimPunchAngle );

  // Calculate recoil-adjusted view angles
  Vector recoilAdjustedViewAngles = currentViewAngles;
  recoilAdjustedViewAngles.x += aimPunchAngle.x * 2.f;
  recoilAdjustedViewAngles.y += aimPunchAngle.y * 2.f;

  // Normalize angles
  Vector::Normalize(recoilAdjustedViewAngles);

  // Convert angles to forward vector
  Vector forwardVec;
  Vector::Angle(recoilAdjustedViewAngles, forwardVec);

  // Scale forward vector for projection
  forwardVec.x *= 10000.f;
  forwardVec.y *= 10000.f;
  forwardVec.z *= 10000.f;

  // CLEAN: Use GameData for player position
  Vector pawnOrigin = GameData::getPlayerPosition(localPlayerPawn);
  // Note: 0xCB0 offset might need to be added to GameData in the future
  Vector viewOffset = driver::read_memory<Vector>( g_driver , localPlayerPawn + 0xCB0 );

  // Calculate start position (eye position)
  Vector startPos = pawnOrigin;
  Vector::Add(startPos, viewOffset);

  // Calculate end position
  Vector endPos = startPos;
  Vector::Add(endPos, forwardVec);

  // Convert to screen coordinates using GameData
  Vector endScreen;
  view_matrix_t viewMatrix = GameData::getViewMatrix();
  if (Vector::world_to_screen(viewMatrix, endPos, endScreen)) {
    // Draw the recoil crosshair dot
    Render::Dot(endScreen.x, endScreen.y, globals::Misc::RecoilCrosshair::size, globals::Misc::RecoilCrosshair::Color);
  }
}

void MISC::SniperCrosshair() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // CLEAN: Use GameData convenience wrapper system
  uintptr_t pawn = GameData::getLocalPlayerPawn();
  if (!pawn) return;

  bool scoped = GameData::getIsScoped(pawn);
  if (scoped) return;

  // Read weapon and item index using GameData
  uintptr_t weapon = GameData::getActiveWeapon(pawn);
  uint16_t idx = GameData::getWeaponItemIndex(weapon);

  // Only specific snipers
  if( idx != static_cast<uint16_t>(ItemIndex::WeaponAwp) && idx != static_cast<uint16_t>(ItemIndex::WeaponSsg08) &&
      idx != static_cast<uint16_t>(ItemIndex::WeaponG3sg1) && idx != static_cast<uint16_t>(ItemIndex::WeaponScar20)
  ) return;

  // Crosshair settings
  float gap       = globals::Crosshair::gap;
  float length    = globals::Crosshair::length;
  float thickness = globals::Crosshair::thickness;
  ImVec4 color     = globals::Crosshair::Color;

  // Determine pixel-aligned center based on thickness parity
  bool oddThickness = (static_cast<int>(thickness) % 2 != 0);
  float cx = globals::Screen::width  * 0.5f;
  float cy = globals::Screen::height * 0.5f;

  if (oddThickness) {
    cx = std::floor(cx) + 0.5f;
    cy = std::floor(cy) + 0.5f;
  } else {
    cx = std::round(cx);
    cy = std::round(cy);
  }

  // Calculate odd-thickness offsets
  float oddOffsetTop  = oddThickness ?  1.0f : 0.0f;   // hier: +1 px verschiebt obere Linie nach unten
  float oddOffsetLeft = oddThickness ?  1.0f : 0.0f;   // hier: +1 px verschiebt linke Linie nach rechts

  // Draw lines via filled rects for pixel perfection
  float halfThickness  = thickness * 0.5f;
  // Vertical top line
  Render::DrawRectFilled(
    cx - halfThickness,
    cy - gap - length + oddOffsetTop,    // hier angewendet: korrigiert 1 px nach unten
    thickness,
    length,
    color,
    0.0f
  );

  // Vertical bottom line
  Render::DrawRectFilled(
    cx - halfThickness,
    cy + gap,
    thickness,
    length,
    color,
    0.0f
  );

  // Horizontal left line
  Render::DrawRectFilled(
    cx - gap - length + oddOffsetLeft,   // hier angewendet: korrigiert 1 px nach rechts
    cy - halfThickness,
    length,
    thickness,
    color,
    0.0f
  );

  // Horizontal right line
  Render::DrawRectFilled(
    cx + gap,
    cy - halfThickness,
    length,
    thickness,
    color,
    0.0f
  );


  // Optional center dot
  if (globals::Crosshair::dotenabled) {
    float dotR = max(globals::Crosshair::dotSize, 0.5f);
    Render::fDot(cx, cy, dotR, color);
  }
}

void MISC::Hitmarker() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Damage tracking variables
  static int oldDamage = 0, oldNumRoundKills = 0, oldNumRoundHeadshotKills = 0;

  // CLEAN: Use GameData convenience wrapper system
  uintptr_t localPlayer = GameData::getLocalPlayerController();
  if (!localPlayer) return;

  // Get ActionTrackingServices and damage data
  const auto* gameVars = GameVars::getInstance();
  uintptr_t   ActionTrackingServices = driver::read_memory<uintptr_t>( g_driver, localPlayer + Offset::PlayerController::m_pActionTrackingServices );
  if (!ActionTrackingServices) return;

  int DamageDealt            = driver::read_memory<int>( g_driver, ActionTrackingServices + Offset::ActionTracking::m_unTotalRoundDamageDealt );
  int NumRoundKills          = driver::read_memory<int>( g_driver, ActionTrackingServices + Offset::ActionTracking::m_iNumRoundKills );
  int NumRoundKillsHeadshots = driver::read_memory<int>( g_driver, ActionTrackingServices + Offset::ActionTracking::m_iNumRoundKillsHeadshots );

  // Check for damage increase to trigger hitmarker
  if (DamageDealt > oldDamage) {
    if (globals::Hitmarker::enabled) {
      globals::flHitmarkerAlpha = 1.f;
    }
  }

  // Update old values
  oldNumRoundHeadshotKills = NumRoundKillsHeadshots;
  oldNumRoundKills = NumRoundKills;
  oldDamage = DamageDealt;

  // Hitmarker rendering logic
  static std::chrono::time_point<std::chrono::high_resolution_clock> hitmarkerStartTime;
  static bool hitmarkerActive = false;

  if (globals::flHitmarkerAlpha > 0 && !hitmarkerActive) {
    // Start the timer when hitmarker becomes visible
    hitmarkerStartTime = std::chrono::high_resolution_clock::now();
    hitmarkerActive = true;
  }

  if (globals::flHitmarkerAlpha <= 0)
    return;

  if (hitmarkerActive) {
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
      std::chrono::high_resolution_clock::now() - hitmarkerStartTime);

    // Check if the hitmarker duration has elapsed
    if (elapsed.count() > globals::Hitmarker::duration) {
      globals::flHitmarkerAlpha = 0;
      hitmarkerActive = false;
      return;
    }
  }

  // Draw hitmarker lines
  Render::fLine(globals::centerX + globals::Hitmarker::gap, globals::centerY - globals::Hitmarker::gap,
    globals::centerX + globals::Hitmarker::gap + globals::Hitmarker::length, globals::centerY - globals::Hitmarker::gap - globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Top Right

  Render::fLine(globals::centerX + globals::Hitmarker::gap, globals::centerY + globals::Hitmarker::gap,
    globals::centerX + globals::Hitmarker::gap + globals::Hitmarker::length, globals::centerY + globals::Hitmarker::gap + globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Bottom Right

  Render::fLine(globals::centerX - globals::Hitmarker::gap, globals::centerY - globals::Hitmarker::gap,
    globals::centerX - globals::Hitmarker::gap - globals::Hitmarker::length, globals::centerY - globals::Hitmarker::gap - globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Top Left

  Render::fLine(globals::centerX - globals::Hitmarker::gap, globals::centerY + globals::Hitmarker::gap,
    globals::centerX - globals::Hitmarker::gap - globals::Hitmarker::length, globals::centerY + globals::Hitmarker::gap + globals::Hitmarker::length,
    globals::Hitmarker::Color, globals::Hitmarker::thickness);  // Bottom Left

  // Gradually reduce alpha
  if (globals::flHitmarkerAlpha < 0)
    globals::flHitmarkerAlpha = 0;
}

void MISC::Sound() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Damage tracking variables
  static int oldDamage = 0, oldNumRoundKills = 0, oldNumRoundHeadshotKills = 0;

  // CLEAN: Use GameData convenience wrapper system
  uintptr_t localPlayer = GameData::getLocalPlayerController();
  if (!localPlayer) return;

  // Get ActionTrackingServices and damage data
  const auto* gameVars = GameVars::getInstance();
  uintptr_t ActionTrackingServices = driver::read_memory<uintptr_t>(g_driver, localPlayer + Offset::PlayerController::m_pActionTrackingServices);
  if (!ActionTrackingServices) return;

  int DamageDealt = driver::read_memory<int>(g_driver, ActionTrackingServices + Offset::ActionTracking::m_unTotalRoundDamageDealt);
  int NumRoundKills = driver::read_memory<int>(g_driver, ActionTrackingServices + Offset::ActionTracking::m_iNumRoundKills);
  int NumRoundKillsHeadshots = driver::read_memory<int>(g_driver, ActionTrackingServices + Offset::ActionTracking::m_iNumRoundKillsHeadshots);

  // Sound path setup
  std::string soundPath;
  char documentsPath[MAX_PATH];
  if (SHGetFolderPathA(NULL, CSIDL_MYDOCUMENTS, NULL, SHGFP_TYPE_CURRENT, documentsPath) == S_OK) {
    soundPath = std::string(documentsPath) + "\\Nebula\\sound\\";
  } else {
    soundPath = ".\\sound\\"; // Fallback
  }

  std::string fullPathHit = soundPath + globals::Hitsound::sound;
  std::string fullPathKill = soundPath + globals::Killsound::sound;
  std::string fullPathKillHeadshot = soundPath + globals::HsKillsound::sound;

  // Volume control variables (static to persist between calls)
  static DWORD originalVolume = 0;
  static bool volumeInitialized = false;
  static auto restoreTime = std::chrono::steady_clock::now();
  static bool needsRestore = false;

  // Initialize original volume once
  if (!volumeInitialized) {
    if (waveOutGetVolume(0, &originalVolume) == MMSYSERR_NOERROR) {
      volumeInitialized = true;
    }
  }

  // Check if we need to restore volume (frame-based restoration)
  auto now = std::chrono::steady_clock::now();
  if (needsRestore && now >= restoreTime && volumeInitialized) {
    waveOutSetVolume(0, originalVolume);
    needsRestore = false;
  }

  // Safe sound playing with volume control
  auto playSound = [&](bool soundEnabled, const std::string& path) -> bool {
    namespace fs = std::filesystem;
    if (soundEnabled && fs::exists(path.c_str()) && globals::Sound::volume > 0.0f && volumeInitialized) {

      // Calculate new volume (0-100 to 0-65535)
      DWORD newVolume = static_cast<DWORD>((globals::Sound::volume / 100.0f) * 65535.0f);
      DWORD stereoVolume = (newVolume << 16) | newVolume;

      // Set volume temporarily
      waveOutSetVolume(0, stereoVolume);

      // Play sound
      BOOL result = sndPlaySoundA(path.c_str(), SND_FILENAME | SND_ASYNC);

      // Schedule volume restoration
      restoreTime = std::chrono::steady_clock::now() + std::chrono::milliseconds(100);
      needsRestore = true;

      return result != FALSE;
    }
    return false;
  };

  // Check for damage increase to trigger sounds
  if (DamageDealt > oldDamage) {
    if (globals::Sound::enabled) {
      if (NumRoundKillsHeadshots > oldNumRoundHeadshotKills) {
        if (!playSound(globals::HsKillsound::enabled, fullPathKillHeadshot))
          if (!playSound(globals::Killsound::enabled, fullPathKill))
            playSound(globals::Hitsound::enabled, fullPathHit);
      }
      else if (NumRoundKills > oldNumRoundKills) {
        if (!playSound(globals::Killsound::enabled, fullPathKill))
          playSound(globals::Hitsound::enabled, fullPathHit);
      }
      else {
        playSound(globals::Hitsound::enabled, fullPathHit);
      }
    }
  }

  // Update old values
  oldNumRoundHeadshotKills = NumRoundKillsHeadshots;
  oldNumRoundKills = NumRoundKills;
  oldDamage = DamageDealt;
}

void MISC::Keystrokes(bool menuIsOpen) {
  // Set initial position on first frame
  if (globals::isFirstFrameKeystrokes) {
    globals::isFirstFrameKeystrokes = false;
  }

  // Calculate dimensions with scaling (using natural text spacing)
  float scale = globals::Keystrokes::scale;

  // Use the large FreeType font for calculations
  ImFont* keyFont = font; // 40f FreeType font
  float fontSize = 40.0f * scale;

  // Calculate natural text sizes and spacing
  float wWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "W").x;
  float aWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "A").x;
  float sWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "S").x;
  float dWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "D").x;
  float spaceWidth = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "SPACE").x;

  // Natural spacing between letters (like ImGui::SameLine())
  float letterSpacing = fontSize * 0.3f; // Natural spacing based on font size
  float lineHeight = keyFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "W").y;
  float verticalSpacing = fontSize * 0.1f; // Much smaller vertical spacing

  // Colors
  ImVec4 normalColor = globals::Keystrokes::Color;
  ImVec4 pressedColor = globals::Keystrokes::pressedColor;

  // Get background draw list (NEVER fades!)
  ImDrawList* drawList = ImGui::GetBackgroundDrawList();

  // Base position
  ImVec2 basePos = ImVec2(globals::Keystrokes::posX, globals::Keystrokes::posY);

  // Handle dragging with visible drag point when menu is open
  static bool isDragging = false;
  static ImVec2 dragOffset;

  // Calculate total area for dragging (based on natural text layout)
  float asdRowWidth = aWidth + sWidth + dWidth + letterSpacing * 2; // A-S-D row width
  float totalWidth = (std::max)((std::max)(wWidth, asdRowWidth), spaceWidth); // Widest element
  float totalHeight = lineHeight * 3 + verticalSpacing * 2; // 3 rows with tight vertical spacing

  if (menuIsOpen) {
    ImVec2 mousePos = ImGui::GetMousePos();

    // Check if mouse is over keystrokes area
    bool mouseOverKeystrokes = mousePos.x >= basePos.x && mousePos.x <= basePos.x + totalWidth &&
                              mousePos.y >= basePos.y && mousePos.y <= basePos.y + totalHeight;

    // Start dragging if clicked on keystrokes area
    if (ImGui::IsMouseClicked(ImGuiMouseButton_Left) && mouseOverKeystrokes) {
      isDragging = true;
      dragOffset = ImVec2(mousePos.x - basePos.x, mousePos.y - basePos.y);
    }

    // Handle dragging
    if (isDragging) {
      if (ImGui::IsMouseDown(ImGuiMouseButton_Left)) {
        globals::Keystrokes::posX = mousePos.x - dragOffset.x;
        globals::Keystrokes::posY = mousePos.y - dragOffset.y;
        basePos = ImVec2(globals::Keystrokes::posX, globals::Keystrokes::posY);
      } else {
        isDragging = false;
      }
    }
  } else {
    // Menu is closed - stop dragging
    isDragging = false;
  }

  // Draw keys with natural text positioning (like original ImGui layout)

  // W key (top row, centered)
  bool wPressed = GetAsyncKeyState('W') & 0x8000;
  ImVec4 wColor = wPressed ? pressedColor : normalColor;
  ImVec2 wPos = ImVec2(basePos.x + (totalWidth - wWidth) * 0.5f, basePos.y);
  drawList->AddText(keyFont, fontSize, wPos, ImColor(wColor), "W");

  // A-S-D keys (middle row, naturally spaced)
  float asdStartX = basePos.x + (totalWidth - asdRowWidth) * 0.5f; // Center the A-S-D row
  float asdY = basePos.y + lineHeight + verticalSpacing;

  // A key
  bool aPressed = GetAsyncKeyState('A') & 0x8000;
  ImVec4 aColor = aPressed ? pressedColor : normalColor;
  ImVec2 aPos = ImVec2(asdStartX, asdY);
  drawList->AddText(keyFont, fontSize, aPos, ImColor(aColor), "A");

  // S key
  bool sPressed = GetAsyncKeyState('S') & 0x8000;
  ImVec4 sColor = sPressed ? pressedColor : normalColor;
  ImVec2 sPos = ImVec2(asdStartX + aWidth + letterSpacing, asdY);
  drawList->AddText(keyFont, fontSize, sPos, ImColor(sColor), "S");

  // D key
  bool dPressed = GetAsyncKeyState('D') & 0x8000;
  ImVec4 dColor = dPressed ? pressedColor : normalColor;
  ImVec2 dPos = ImVec2(asdStartX + aWidth + sWidth + letterSpacing * 2, asdY);
  drawList->AddText(keyFont, fontSize, dPos, ImColor(dColor), "D");

  // Space bar (bottom row, centered)
  bool spacePressed = GetAsyncKeyState(VK_SPACE) & 0x8000;
  ImVec4 spaceColor = spacePressed ? pressedColor : normalColor;

  float spaceY = basePos.y + (lineHeight + verticalSpacing) * 2; // Third row with tight spacing
  ImVec2 spacePos = ImVec2(basePos.x + (totalWidth - spaceWidth) * 0.5f, spaceY);

  // Draw space bar text with large FreeType font
  drawList->AddText(keyFont, fontSize, spacePos, ImColor(spaceColor), "SPACE");
}

void MISC::Watermark() {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Get FPS
  static std::chrono::time_point<std::chrono::steady_clock> lastUpdate = std::chrono::steady_clock::now();
  auto now = std::chrono::steady_clock::now();
  static float currentFPS = 0.0f;

  if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUpdate).count() >= 500) {
    lastUpdate = now;
    currentFPS = ImGui::GetIO().Framerate;
  }

  // Get memory information
  MEMORYSTATUSEX memInfo;
  memInfo.dwLength = sizeof(MEMORYSTATUSEX);
  GlobalMemoryStatusEx(&memInfo);

  DWORDLONG totalPhysMem = memInfo.ullTotalPhys;
  DWORDLONG physMemUsed = totalPhysMem - memInfo.ullAvailPhys;

  // Convert to GB
  float usedGB = physMemUsed / (1024.0f * 1024.0f * 1024.0f);
  float totalGB = totalPhysMem / (1024.0f * 1024.0f * 1024.0f);

  // Individual component fade animations
  static float nebulaAlpha = 1.0f;
  static float fpsAlpha = 1.0f;
  static float ramAlpha = 1.0f;
  static float betaAlpha = 1.0f;

  static bool lastNebulaState = globals::Watermark::showNebula;
  static bool lastFpsState = globals::Watermark::showFPS;
  static bool lastRamState = globals::Watermark::showRAM;
  static bool lastBetaState = globals::Watermark::showBETA;

  float fadeSpeed = 8.f; // Half speed for smoother animation
  float deltaTime = ImGui::GetIO().DeltaTime;

  // Handle individual component fade animations
  if (globals::Watermark::showNebula != lastNebulaState) {
    lastNebulaState = globals::Watermark::showNebula;
  }
  if (globals::Watermark::showFPS != lastFpsState) {
    lastFpsState = globals::Watermark::showFPS;
  }
  if (globals::Watermark::showRAM != lastRamState) {
    lastRamState = globals::Watermark::showRAM;
  }
  if (globals::Watermark::showBETA != lastBetaState) {
    lastBetaState = globals::Watermark::showBETA;
  }

  // Animate alphas
  nebulaAlpha += (globals::Watermark::showNebula ? 1.0f : 0.0f - nebulaAlpha) * fadeSpeed * deltaTime;
  fpsAlpha += (globals::Watermark::showFPS ? 1.0f : 0.0f - fpsAlpha) * fadeSpeed * deltaTime;
  ramAlpha += (globals::Watermark::showRAM ? 1.0f : 0.0f - ramAlpha) * fadeSpeed * deltaTime;
  betaAlpha += (globals::Watermark::showBETA ? 1.0f : 0.0f - betaAlpha) * fadeSpeed * deltaTime;

  // Clamp alphas
  if (nebulaAlpha < 0.0f) nebulaAlpha = 0.0f;
  if (nebulaAlpha > 1.0f) nebulaAlpha = 1.0f;
  if (fpsAlpha < 0.0f) fpsAlpha = 0.0f;
  if (fpsAlpha > 1.0f) fpsAlpha = 1.0f;
  if (ramAlpha < 0.0f) ramAlpha = 0.0f;
  if (ramAlpha > 1.0f) ramAlpha = 1.0f;
  if (betaAlpha < 0.0f) betaAlpha = 0.0f;
  if (betaAlpha > 1.0f) betaAlpha = 1.0f;

  // Build component strings
  char fpsText[32];
  snprintf(fpsText, sizeof(fpsText), "FPS: %.0f", currentFPS);
  char ramText[64];
  snprintf(ramText, sizeof(ramText), "RAM: %.1f/%.1f", usedGB, totalGB);

  // Calculate individual component sizes for smooth positioning
  ImFont* watermarkFont = espfont;
  float fontSize = 9.0f;

  float nebulaSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "NEBULA").x;
  float fpsSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, fpsText).x;
  float ramSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, ramText).x;
  float betaSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "BETA").x;
  float sepSize = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, " | ").x;

  // Calculate total animated width including animated separators
  float totalWidth = 0.0f;

  // Add Nebula
  if (nebulaAlpha > 0.01f) {
    totalWidth += nebulaSize * nebulaAlpha;
  }

  // Add separator between Nebula and FPS (fades with both components)
  if (nebulaAlpha > 0.01f && fpsAlpha > 0.01f) {
    float sepAlpha = (nebulaAlpha < fpsAlpha) ? nebulaAlpha : fpsAlpha; // Use minimum alpha
    totalWidth += sepSize * sepAlpha;
  }

  // Add FPS
  if (fpsAlpha > 0.01f) {
    totalWidth += fpsSize * fpsAlpha;
  }

  // Add separator between FPS and RAM
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f) && ramAlpha > 0.01f) {
    float prevAlpha = (nebulaAlpha > fpsAlpha) ? nebulaAlpha : fpsAlpha; // Use maximum of previous components
    float sepAlpha = (prevAlpha < ramAlpha) ? prevAlpha : ramAlpha;
    totalWidth += sepSize * sepAlpha;
  }

  // Add RAM
  if (ramAlpha > 0.01f) {
    totalWidth += ramSize * ramAlpha;
  }

  // Add separator between RAM and BETA
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f || ramAlpha > 0.01f) && betaAlpha > 0.01f) {
    float prevAlpha = nebulaAlpha;
    if (fpsAlpha > prevAlpha) prevAlpha = fpsAlpha;
    if (ramAlpha > prevAlpha) prevAlpha = ramAlpha;
    float sepAlpha = (prevAlpha < betaAlpha) ? prevAlpha : betaAlpha;
    totalWidth += sepSize * sepAlpha;
  }

  // Add BETA
  if (betaAlpha > 0.01f) {
    totalWidth += betaSize * betaAlpha;
  }

  // If nothing is visible, don't render
  if (totalWidth <= 0.0f) {
    return;
  }

  // Position in top-right corner
  float screenWidth = static_cast<float>(globals::Screen::width);
  float posX = screenWidth - totalWidth - 5.0f;
  float posY = 3.0f;

  // Get background draw list
  ImDrawList* drawList = ImGui::GetBackgroundDrawList();

  // Draw each component individually with smooth animated separators
  float currentX = posX;

  // Draw Nebula
  if (nebulaAlpha > 0.01f) {
    ImVec4 nebulaColor = globals::Watermark::Color;
    nebulaColor.w *= nebulaAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(nebulaColor), "NEBULA");
    currentX += nebulaSize * nebulaAlpha;
  }

  // Draw separator between Nebula and FPS with smooth fade
  if (nebulaAlpha > 0.01f && fpsAlpha > 0.01f) {
    float sepAlpha = (nebulaAlpha < fpsAlpha) ? nebulaAlpha : fpsAlpha;
    ImVec4 sepColor = globals::Watermark::Color;
    sepColor.w *= sepAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(sepColor), " | ");
    currentX += sepSize * sepAlpha;
  }

  // Draw FPS
  if (fpsAlpha > 0.01f) {
    ImVec4 fpsColor = globals::Watermark::Color;
    fpsColor.w *= fpsAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(fpsColor), fpsText);
    currentX += fpsSize * fpsAlpha;
  }

  // Draw separator between FPS and RAM with smooth fade
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f) && ramAlpha > 0.01f) {
    float prevAlpha = (nebulaAlpha > fpsAlpha) ? nebulaAlpha : fpsAlpha;
    float sepAlpha = (prevAlpha < ramAlpha) ? prevAlpha : ramAlpha;
    ImVec4 sepColor = globals::Watermark::Color;
    sepColor.w *= sepAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(sepColor), " | ");
    currentX += sepSize * sepAlpha;
  }

  // Draw RAM
  if (ramAlpha > 0.01f) {
    ImVec4 ramColor = globals::Watermark::Color;
    ramColor.w *= ramAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(ramColor), ramText);
    currentX += ramSize * ramAlpha;
  }

  // Draw separator between RAM and BETA with smooth fade
  if ((nebulaAlpha > 0.01f || fpsAlpha > 0.01f || ramAlpha > 0.01f) && betaAlpha > 0.01f) {
    float prevAlpha = nebulaAlpha;
    if (fpsAlpha > prevAlpha) prevAlpha = fpsAlpha;
    if (ramAlpha > prevAlpha) prevAlpha = ramAlpha;
    float sepAlpha = (prevAlpha < betaAlpha) ? prevAlpha : betaAlpha;
    ImVec4 sepColor = globals::Watermark::Color;
    sepColor.w *= sepAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(sepColor), " | ");
    currentX += sepSize * sepAlpha;
  }

  // Draw BETA
  if (betaAlpha > 0.01f) {
    ImVec4 betaColor = globals::Watermark::Color;
    betaColor.w *= betaAlpha;
    drawList->AddText(watermarkFont, fontSize, ImVec2(currentX, posY), ImColor(betaColor), "BETA");
  }

  // Draw gradient line perfectly aligned with animated text bounds
  float textHeight = watermarkFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, "NEBULA").y;
  float lineY = posY + textHeight + 2.0f; // 2px below text
  float lineStartX = posX; // Start exactly where text starts
  float lineEndX = posX + totalWidth; // End exactly where text ends
  float lineWidth = totalWidth; // Total animated width of the line

  // Draw gradient line from text start to text end with center-to-edges fade
  int segments = 50; // More segments for smoother gradient
  float centerX = posX + (totalWidth * 0.5f);
  float halfWidth = totalWidth * 0.5f;

  for (int i = 0; i < segments; i++) {
    float t = static_cast<float>(i) / (segments - 1); // 0 to 1

    // Calculate position from left edge to right edge
    float currentX = lineStartX + (t * lineWidth);
    float nextX = lineStartX + ((t + 1.0f/(segments-1)) * lineWidth);

    // Calculate distance from center (0 = center, 1 = edge)
    float distanceFromCenter = abs(currentX - centerX) / halfWidth;

    // Create fade from center to edges
    float alpha = globals::Watermark::gradientColor.w * (1.0f - distanceFromCenter);

    ImVec4 lineColor = ImVec4(globals::Watermark::gradientColor.x, globals::Watermark::gradientColor.y,
                              globals::Watermark::gradientColor.z, alpha);

    // Only draw if we're not at the last segment
    if (i < segments - 1) {
      drawList->AddLine(ImVec2(currentX, lineY), ImVec2(nextX, lineY), ImColor(lineColor), 1.0f);
    }
  }
}

void MISC::Spectators(bool menuIsOpen) {
  // Check if GameData system is ready
  if (!GameData::isInitialized()) {
    return;
  }

  // Get spectator list
  auto spectatorList = SpectatorData::getSpectatorListCopy();

  // Use large FreeType font for crisp scaling
  ImFont* spectatorFont = font;
  float baseFontSize = 40.0f;
  float fontSize = baseFontSize * globals::Spectators::scale;

  // Individual spectator fade animations (like watermark components)
  static std::map<std::string, float> spectatorAlphas; // Name -> Alpha
  static std::map<std::string, bool> spectatorStates;  // Name -> Should be visible
  static std::vector<std::string> lastSpectatorList;
  static std::vector<std::string> displayList; // All spectators to display (including fading out)

  // Vertical movement animation (like watermark slide effects)
  static std::map<std::string, float> spectatorYOffsets; // Name -> Y offset for smooth movement

  float fadeSpeed = 8.0f;
  float deltaTime = ImGui::GetIO().DeltaTime;

  // Handle spectator list changes
  bool listChanged = (spectatorList != lastSpectatorList);

  if (listChanged) {
    // Mark current spectators as should be visible
    for (const auto& name : spectatorList) {
      spectatorStates[name] = true;
      if (spectatorAlphas.find(name) == spectatorAlphas.end()) {
        spectatorAlphas[name] = 0.0f; // New spectator starts invisible
        spectatorYOffsets[name] = 30.0f; // Start below target position (slide up effect)
      }
    }

    // Mark removed spectators as should fade out
    for (const auto& oldName : lastSpectatorList) {
      bool stillExists = false;
      for (const auto& newName : spectatorList) {
        if (oldName == newName) {
          stillExists = true;
          break;
        }
      }
      if (!stillExists) {
        spectatorStates[oldName] = false; // Should fade out
      }
    }

    lastSpectatorList = spectatorList;
  }

  // Build display list (current + fading out spectators)
  displayList.clear();

  // Add current spectators first
  for (const auto& name : spectatorList) {
    displayList.push_back(name);
  }

  // Add fading out spectators
  for (const auto& pair : spectatorAlphas) {
    const std::string& name = pair.first;
    float alpha = pair.second;

    // If not in current list but still has some alpha, add to display
    if (alpha > 0.01f) {
      bool inCurrentList = false;
      for (const auto& currentName : spectatorList) {
        if (name == currentName) {
          inCurrentList = true;
          break;
        }
      }
      if (!inCurrentList) {
        displayList.push_back(name);
      }
    }
  }

  // Animate individual spectator alphas (exactly like watermark)
  for (auto& pair : spectatorAlphas) {
    const std::string& name = pair.first;
    float& alpha = pair.second;

    bool shouldBeVisible = (spectatorStates.find(name) != spectatorStates.end()) ? spectatorStates[name] : false;

    alpha += (shouldBeVisible ? 1.0f : 0.0f - alpha) * fadeSpeed * deltaTime;

    // Clamp alphas
    if (alpha < 0.0f) alpha = 0.0f;
    if (alpha > 1.0f) alpha = 1.0f;
  }

  // Animate individual spectator Y offsets (smooth vertical movement)
  for (auto& pair : spectatorYOffsets) {
    const std::string& name = pair.first;
    float& yOffset = pair.second;

    bool shouldBeVisible = (spectatorStates.find(name) != spectatorStates.end()) ? spectatorStates[name] : false;

    // Target Y offset: 0.0f when visible, 30.0f when fading out (slide down effect)
    float targetOffset = shouldBeVisible ? 0.0f : 30.0f;
    yOffset += (targetOffset - yOffset) * fadeSpeed * deltaTime;

    // Clamp Y offsets
    if (yOffset < 0.0f) yOffset = 0.0f;
    if (yOffset > 30.0f) yOffset = 30.0f;
  }

  // Remove completely faded out spectators
  auto it = spectatorAlphas.begin();
  while (it != spectatorAlphas.end()) {
    if (it->second <= 0.01f && spectatorStates[it->first] == false) {
      spectatorStates.erase(it->first);
      spectatorYOffsets.erase(it->first); // Also remove Y offset
      it = spectatorAlphas.erase(it);
    } else {
      ++it;
    }
  }

  // Calculate component sizes for display list
  std::string title = "SPECTATORS";
  ImVec2 titleSize = spectatorFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, title.c_str());

  std::map<std::string, ImVec2> nameSizes;
  float maxNameWidth = titleSize.x;

  for (const auto& spectatorName : displayList) {
    ImVec2 nameSize = spectatorFont->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, spectatorName.c_str());
    nameSizes[spectatorName] = nameSize;
    if (nameSize.x > maxNameWidth) {
      maxNameWidth = nameSize.x;
    }
  }

  // Calculate total animated height (like watermark total width)
  float lineSpacing = 2.0f * globals::Spectators::scale;
  float totalAnimatedHeight = titleSize.y + 5.0f; // Title + gap

  for (const auto& spectatorName : displayList) {
    if (spectatorAlphas.find(spectatorName) != spectatorAlphas.end()) {
      float alpha = spectatorAlphas[spectatorName];
      if (alpha > 0.01f && nameSizes.find(spectatorName) != nameSizes.end()) {
        totalAnimatedHeight += (nameSizes[spectatorName].y + lineSpacing) * alpha;
      }
    }
  }

  // Position
  ImVec2 basePos = ImVec2(globals::Spectators::posX, globals::Spectators::posY);

  // Handle dragging
  static bool isDragging = false;
  static ImVec2 dragOffset;

  if (menuIsOpen) {
    ImVec2 mousePos = ImGui::GetMousePos();

    bool mouseOverSpectators = mousePos.x >= basePos.x && mousePos.x <= basePos.x + maxNameWidth &&
      mousePos.y >= basePos.y && mousePos.y <= basePos.y + totalAnimatedHeight;

    if (ImGui::IsMouseClicked(ImGuiMouseButton_Left) && mouseOverSpectators) {
      isDragging = true;
      dragOffset = ImVec2(mousePos.x - basePos.x, mousePos.y - basePos.y);
    }

    if (isDragging) {
      if (ImGui::IsMouseDown(ImGuiMouseButton_Left)) {
        globals::Spectators::posX = mousePos.x - dragOffset.x;
        globals::Spectators::posY = mousePos.y - dragOffset.y;
        basePos = ImVec2(globals::Spectators::posX, globals::Spectators::posY);
      } else {
        isDragging = false;
      }
    }
  } else {
    isDragging = false;
  }

  // Always render title and underline; skip only if component fully transparent (unlikely)
  // (No early return here, so title/underline show even when list empty)


  // Get background draw list
  ImDrawList* drawList = ImGui::GetBackgroundDrawList();

  // Draw title (always visible)
  float titleX = basePos.x + (maxNameWidth - titleSize.x) * 0.5f;
  ImVec4 titleColor = globals::Spectators::Color;
  drawList->AddText(spectatorFont, fontSize, ImVec2(titleX, basePos.y), ImColor(titleColor), title.c_str());

  // Draw gradient line perfectly aligned with animated title text bounds
  float lineY = basePos.y + titleSize.y + 2.0f; // 2px below the title
  float lineStartX = basePos.x;              // Starts at the left edge of the title block
  float lineWidth = maxNameWidth;            // Uses the maximum text width as total width

  int segments = 50; // More segments for a smoother gradient
  float centerX = lineStartX + (lineWidth * 0.5f);
  float halfWidth = lineWidth * 0.5f;

  for (int i = 0; i < segments; i++) {
    float t = static_cast<float>(i) / (segments - 1); // 0 to 1

    // Calculate position from left edge to right edge
    float currentX = lineStartX + (t * lineWidth);
    float nextX = lineStartX + ((t + 1.0f/(segments-1)) * lineWidth);

    // Calculate distance from center (0 = center, 1 = edge)
    float distanceFromCenter = abs(currentX - centerX) / halfWidth;

    // Create fade from center to edges
    float alpha = globals::Watermark::gradientColor.w * (1.0f - distanceFromCenter);

    ImVec4 lineColor = ImVec4(globals::Spectators::gradientColor.x, globals::Spectators::gradientColor.y,
      globals::Spectators::gradientColor.z, alpha);

    // Only draw if we're not at the last segment
    if (i < segments - 1) {
      drawList->AddLine(ImVec2(currentX, lineY), ImVec2(nextX, lineY), ImColor(lineColor), 1.0f);
    }
  }

  // Draw spectator names with smooth stacking and vertical movement (like watermark)
  float currentY = basePos.y + titleSize.y + 5.0f;

  for (const auto& spectatorName : displayList) {
    if (spectatorAlphas.find(spectatorName) == spectatorAlphas.end()) continue;

    float nameAlpha = spectatorAlphas[spectatorName];
    if (nameAlpha > 0.01f && nameSizes.find(spectatorName) != nameSizes.end()) {
      const auto& nameSize = nameSizes[spectatorName];

      // Center horizontally
      float nameX = basePos.x + (maxNameWidth - nameSize.x) * 0.5f;

      // Get Y offset for smooth vertical movement
      float yOffset = 0.0f;
      if (spectatorYOffsets.find(spectatorName) != spectatorYOffsets.end()) {
        yOffset = spectatorYOffsets[spectatorName];
      }

      // Apply vertical movement animation
      float animatedY = currentY + yOffset;

      // Smooth fade with vertical movement animation
      ImVec4 nameColor = ImVec4(globals::Spectators::Color.x, globals::Spectators::Color.y, globals::Spectators::Color.z, nameAlpha * 0.9f);

      drawList->AddText(spectatorFont, fontSize, ImVec2(nameX, animatedY), ImColor(nameColor), spectatorName.c_str());

      // Update currentY based on animated height (smooth stacking)
      currentY += (nameSize.y + lineSpacing) * nameAlpha;
    }
  }
}